# 🤖 AI 执行检查清单 (AI EXECUTION CHECKLIST)

## ⚠️ 编程前强制执行 (MANDATORY PRE-PROGRAMMING EXECUTION)

### 🔍 第一步：状态检查 (STATUS CHECK)
```
□ 1. 读取 website_development_workflow.md
□ 2. 确认当前项目阶段
□ 3. 识别下一个 ⏳ 状态的任务
□ 4. 验证前置任务已完成 ✅
□ 5. 检查相关文档是否存在
```

### 🎯 第二步：任务准备 (TASK PREPARATION)
```
□ 1. 更新任务状态为 🔄 (进行中)
□ 2. 记录开始时间
□ 3. 创建备份点 (如需要)
□ 4. 准备所需工具和资源
□ 5. 设置错误处理机制
```

### ⚙️ 第三步：执行验证 (EXECUTION VALIDATION)
```
□ 1. 验证代码语法正确性
□ 2. 检查 Drupal 编码标准
□ 3. 确认文件路径正确
□ 4. 测试功能是否正常
□ 5. 验证响应式设计
```

### 🔄 第四步：数据流验证和端到端测试 (DATA FLOW & E2E VALIDATION)
```
A. 数据流完整性验证 (VALIDATION_RULE_001)
□ 1. 数据创建测试 - 验证数据能正确创建
□ 2. 数据存储验证 - 确认数据正确保存到数据库
□ 3. 数据检索测试 - 验证数据能正确查询和获取
□ 4. 数据显示确认 - 确认数据在前端正确显示
□ 5. 数据关联验证 - 验证数据间的关联关系正确

B. 端到端功能测试 (VALIDATION_RULE_002)
□ 1. 用户界面交互测试 - 模拟用户操作流程
□ 2. 业务逻辑验证 - 确认业务规则正确执行
□ 3. 数据持久化确认 - 验证数据变更正确保存
□ 4. 错误处理测试 - 测试异常情况处理
□ 5. 性能基准测试 - 确认响应时间可接受

C. 集成验证测试 (VALIDATION_RULE_003)
□ 1. 模块依赖验证 - 确认依赖模块正常工作
□ 2. API 接口测试 - 验证接口调用正确
□ 3. 数据库关联确认 - 检查数据库表关系
□ 4. 缓存一致性验证 - 确认缓存机制正常
□ 5. 权限控制测试 - 验证访问权限正确

D. 用户体验验证 (UX_VALIDATION)
□ 1. 界面响应速度测试 - 确认加载时间可接受
□ 2. 操作流程直观性测试 - 验证用户操作直观
□ 3. 错误提示清晰性测试 - 确认错误信息明确
□ 4. 移动端适配测试 - 验证移动设备兼容性
□ 5. 无障碍功能测试 - 确认可访问性功能
```

### 📝 第五步：完成更新 (COMPLETION UPDATE)
```
□ 1. 确认所有验证通过 (ALL VALIDATIONS PASSED)
□ 2. 更新任务状态为 ✅ (已完成)
□ 3. 记录完成时间和验证结果
□ 4. 添加详细备注说明
□ 5. 更新项目日志
□ 6. 提交代码变更
□ 7. 生成验证报告
```

---

## 🚨 约束性检查点 (CONSTRAINT CHECKPOINTS)

### 检查点 A：依赖性验证
```python
def verify_dependencies():
    """验证任务依赖性"""
    # 检查前置任务是否完成
    if not all_prerequisites_completed():
        return False, "前置任务未完成"

    # 检查必需文件是否存在
    if not required_files_exist():
        return False, "缺少必需文件"

    # 检查系统状态
    if not system_ready():
        return False, "系统未就绪"

    return True, "依赖性验证通过"
```

### 检查点 B：质量保证
```python
def quality_assurance():
    """代码质量保证检查"""
    checks = {
        "syntax_valid": check_syntax(),
        "standards_compliant": check_coding_standards(),
        "documentation_complete": check_documentation(),
        "error_handling_present": check_error_handling()
    }

    failed_checks = [k for k, v in checks.items() if not v]

    if failed_checks:
        return False, f"质量检查失败: {failed_checks}"

    return True, "质量保证检查通过"
```

### 检查点 C：功能验证
```python
def functional_verification():
    """功能验证检查"""
    # 基础功能测试
    if not basic_functionality_works():
        return False, "基础功能测试失败"

    # 用户界面测试
    if not ui_elements_responsive():
        return False, "用户界面响应性测试失败"

    # 数据完整性测试
    if not data_integrity_maintained():
        return False, "数据完整性测试失败"

    return True, "功能验证通过"
```

---

## 📋 快速执行模板 (QUICK EXECUTION TEMPLATE)

### 模板 1：内容类型创建
```yaml
TASK_TYPE: content_type_creation
CHECKLIST:
  pre_execution:
    - □ 检查 content_types_plan.md
    - □ 验证字段配置
    - □ 确认分类词汇表存在
  execution:
    - □ 创建内容类型
    - □ 配置字段
    - □ 设置显示模式
    - □ 配置表单显示
  post_execution:
    - □ 测试内容创建
    - □ 验证字段显示
    - □ 更新工作流程状态
```

### 模板 2：主题模板开发
```yaml
TASK_TYPE: theme_template_development
CHECKLIST:
  pre_execution:
    - □ 检查设计规范
    - □ 验证 Tailwind 配置
    - □ 确认模板路径
  execution:
    - □ 创建 Twig 模板
    - □ 添加 CSS 类
    - □ 实现响应式设计
    - □ 添加交互功能
  post_execution:
    - □ 测试模板渲染
    - □ 验证响应式效果
    - □ 检查浏览器兼容性
```

### 模板 3：功能模块开发
```yaml
TASK_TYPE: module_development
CHECKLIST:
  pre_execution:
    - □ 检查模块依赖
    - □ 验证 API 文档
    - □ 确认数据库结构
  execution:
    - □ 编写模块代码
    - □ 实现业务逻辑
    - □ 添加错误处理
    - □ 编写测试用例
  post_execution:
    - □ 运行功能测试
    - □ 验证性能指标
    - □ 检查安全性
```

---

## 🔄 自动化执行脚本 (AUTOMATED EXECUTION SCRIPT)

```python
#!/usr/bin/env python3
"""
AI 自动化执行脚本
USAGE: python ai_execute.py [task_id]
"""

import sys
import json
from datetime import datetime

class AIExecutor:
    def __init__(self):
        self.workflow_file = "website_development_workflow.md"
        self.protocol_file = "AI_PROGRAMMING_PROTOCOL.md"
        self.checklist_file = "AI_EXECUTION_CHECKLIST.md"

    def execute_checklist(self, task_id=None):
        """执行完整的检查清单"""
        print("🤖 AI 执行检查清单启动...")

        # 第一步：状态检查
        print("\n🔍 第一步：状态检查")
        if not self.status_check(task_id):
            print("❌ 状态检查失败，终止执行")
            return False
        print("✅ 状态检查通过")

        # 第二步：任务准备
        print("\n🎯 第二步：任务准备")
        if not self.task_preparation(task_id):
            print("❌ 任务准备失败，终止执行")
            return False
        print("✅ 任务准备完成")

        # 第三步：执行验证（在实际编程后调用）
        print("\n⚙️ 第三步：执行验证（编程完成后调用）")

        # 第四步：完成更新（在验证通过后调用）
        print("\n📝 第四步：完成更新（验证通过后调用）")

        return True

    def status_check(self, task_id):
        """状态检查实现"""
        try:
            # 读取工作流程文件
            with open(self.workflow_file, 'r', encoding='utf-8') as f:
                workflow_content = f.read()

            # 查找下一个待执行任务
            if task_id:
                next_task = self.find_task_by_id(workflow_content, task_id)
            else:
                next_task = self.find_next_pending_task(workflow_content)

            if not next_task:
                print("❌ 未找到待执行任务")
                return False

            print(f"📋 找到任务: {next_task['name']}")
            print(f"📅 计划时间: {next_task.get('planned_time', 'N/A')}")
            print(f"📝 描述: {next_task.get('description', 'N/A')}")

            return True

        except Exception as e:
            print(f"❌ 状态检查错误: {e}")
            return False

    def task_preparation(self, task_id):
        """任务准备实现"""
        try:
            # 更新任务状态为进行中
            self.update_task_status(task_id, "🔄")

            # 记录开始时间
            start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.log_task_start(task_id, start_time)

            print(f"📝 任务状态已更新为进行中")
            print(f"⏰ 开始时间: {start_time}")

            return True

        except Exception as e:
            print(f"❌ 任务准备错误: {e}")
            return False

    def execution_validation(self, task_id):
        """执行验证（编程完成后调用）"""
        print("\n⚙️ 开始执行验证...")

        validation_results = {
            "syntax_check": self.check_syntax(),
            "standards_check": self.check_coding_standards(),
            "functionality_check": self.check_functionality(),
            "responsiveness_check": self.check_responsiveness()
        }

        failed_checks = [k for k, v in validation_results.items() if not v]

        if failed_checks:
            print(f"❌ 验证失败: {failed_checks}")
            return False

        print("✅ 所有验证检查通过")
        return True

    def completion_update(self, task_id, notes=""):
        """完成更新（验证通过后调用）"""
        try:
            # 更新任务状态为已完成
            completion_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.update_task_status(task_id, "✅", completion_time, notes)

            # 更新项目日志
            self.update_project_log(task_id, completion_time, notes)

            print(f"✅ 任务已标记为完成")
            print(f"⏰ 完成时间: {completion_time}")

            return True

        except Exception as e:
            print(f"❌ 完成更新错误: {e}")
            return False

    # 辅助方法实现
    def find_next_pending_task(self, content):
        """查找下一个待执行任务"""
        # 实现逻辑：解析 markdown 表格，找到状态为 ⏳ 的任务
        pass

    def update_task_status(self, task_id, status, completion_time=None, notes=None):
        """更新任务状态"""
        # 实现逻辑：更新工作流程文件中的任务状态
        pass

    def check_syntax(self):
        """语法检查"""
        # 实现逻辑：检查 PHP、Twig、CSS、JS 语法
        return True

    def check_coding_standards(self):
        """编码标准检查"""
        # 实现逻辑：运行 phpcs 等工具
        return True

    def check_functionality(self):
        """功能检查"""
        # 实现逻辑：基础功能测试
        return True

    def check_responsiveness(self):
        """响应式检查"""
        # 实现逻辑：检查响应式设计
        return True

if __name__ == "__main__":
    executor = AIExecutor()

    task_id = sys.argv[1] if len(sys.argv) > 1 else None

    if executor.execute_checklist(task_id):
        print("\n🎉 检查清单执行完成，可以开始编程任务")
    else:
        print("\n🚨 检查清单执行失败，请解决问题后重试")
        sys.exit(1)
```

---

## 🎯 使用说明 (USAGE INSTRUCTIONS)

### 每次编程前必须执行：
```bash
# 1. 运行检查清单
python ai_execute.py

# 2. 或指定特定任务
python ai_execute.py "创建品牌内容类型"

# 3. 编程完成后验证
python ai_execute.py --validate "创建品牌内容类型"

# 4. 标记任务完成
python ai_execute.py --complete "创建品牌内容类型" --notes "已完成所有字段配置"
```

### AI 必须遵循的执行顺序：
1. **读取协议** → `AI_PROGRAMMING_PROTOCOL.md`
2. **执行检查清单** → `AI_EXECUTION_CHECKLIST.md`
3. **查看工作流程** → `website_development_workflow.md`
4. **执行编程任务**
5. **验证和更新状态**
6. **记录执行报告**

---

## ⚠️ 最终约束声明

```
🔒 ABSOLUTE CONSTRAINT 🔒

每次 AI 编程活动前必须：
1. 确认已读取并理解此协议
2. 执行完整的检查清单
3. 验证所有约束条件
4. 获得执行许可后才能开始编程

违反此协议将导致：
- 立即终止当前任务
- 回滚到上一个稳定状态
- 记录违规行为
- 要求重新执行检查清单

NO EXCEPTIONS. NO SHORTCUTS. NO COMPROMISES.
```

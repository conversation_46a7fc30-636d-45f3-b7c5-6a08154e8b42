<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* themes/custom/juyin/templates/content/node--brand.html.twig */
class __TwigTemplate_22d5566a9c85274a539c5b71aab24665 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->extensions[SandboxExtension::class];
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 7
        yield "
<article";
        // line 8
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["attributes"] ?? null), "addClass", ["brand-card", "bg-white", "rounded-lg", "shadow-soft", "overflow-hidden", "hover:shadow-medium", "transition-shadow", "duration-300"], "method", false, false, true, 8), "html", null, true);
        yield ">
  
  ";
        // line 11
        yield "  ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_logo", [], "any", false, false, true, 11)) {
            // line 12
            yield "    <div class=\"brand-logo aspect-w-16 aspect-h-9 bg-gray-100\">
      ";
            // line 13
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_logo", [], "any", false, false, true, 13), "html", null, true);
            yield "
    </div>
  ";
        }
        // line 16
        yield "
  <div class=\"p-6\">
    ";
        // line 19
        yield "    <h2 class=\"text-xl font-semibold text-gray-900 mb-2\">
      <a href=\"";
        // line 20
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["url"] ?? null), "html", null, true);
        yield "\" class=\"hover:text-primary-600 transition-colors duration-200\">
        ";
        // line 21
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["label"] ?? null), "html", null, true);
        yield "
      </a>
    </h2>

    ";
        // line 26
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_category", [], "any", false, false, true, 26)) {
            // line 27
            yield "      <div class=\"brand-category mb-3\">
        ";
            // line 28
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_category", [], "any", false, false, true, 28), "html", null, true);
            yield "
      </div>
    ";
        }
        // line 31
        yield "
    ";
        // line 33
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_description", [], "any", false, false, true, 33)) {
            // line 34
            yield "      <div class=\"brand-description text-gray-600 text-sm mb-4 line-clamp-3\">
        ";
            // line 35
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_description", [], "any", false, false, true, 35), "html", null, true);
            yield "
      </div>
    ";
        }
        // line 38
        yield "
    ";
        // line 40
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_rating", [], "any", false, false, true, 40)) {
            // line 41
            yield "      <div class=\"brand-rating flex items-center mb-4\">
        <div class=\"flex items-center\">
          ";
            // line 43
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(range(1, 5));
            foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
                // line 44
                yield "            <svg class=\"w-4 h-4 ";
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(((($context["i"] <= CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v0 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_rating", [], "any", false, false, true, 44)) && is_array($_v0) || $_v0 instanceof ArrayAccess && in_array($_v0::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v0["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_rating", [], "any", false, false, true, 44), "#items", [], "array", false, false, true, 44)), 0, [], "any", false, false, true, 44), "value", [], "any", false, false, true, 44))) ? ("text-yellow-400") : ("text-gray-300")));
                yield "\" fill=\"currentColor\" viewBox=\"0 0 20 20\">
              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"/>
            </svg>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['i'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 48
            yield "        </div>
        <span class=\"ml-2 text-sm text-gray-600\">
          (";
            // line 50
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v1 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_rating", [], "any", false, false, true, 50)) && is_array($_v1) || $_v1 instanceof ArrayAccess && in_array($_v1::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v1["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_brand_rating", [], "any", false, false, true, 50), "#items", [], "array", false, false, true, 50)), 0, [], "any", false, false, true, 50), "value", [], "any", false, false, true, 50), "html", null, true);
            yield ")
        </span>
      </div>
    ";
        }
        // line 54
        yield "
    ";
        // line 56
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_count", [], "any", false, false, true, 56)) {
            // line 57
            yield "      <div class=\"product-count text-sm text-gray-500 mb-4\">
        ";
            // line 58
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v2 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_count", [], "any", false, false, true, 58)) && is_array($_v2) || $_v2 instanceof ArrayAccess && in_array($_v2::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v2["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_count", [], "any", false, false, true, 58), "#items", [], "array", false, false, true, 58)), 0, [], "any", false, false, true, 58), "value", [], "any", false, false, true, 58), "html", null, true);
            yield " 个产品
      </div>
    ";
        }
        // line 61
        yield "
    ";
        // line 63
        yield "    <div class=\"flex space-x-3\">
      <a href=\"";
        // line 64
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["url"] ?? null), "html", null, true);
        yield "\" class=\"flex-1 bg-primary-600 text-white text-center py-2 px-4 rounded-md hover:bg-primary-700 transition-colors duration-200 text-sm font-medium\">
        查看详情
      </a>
      <button class=\"favorite-btn p-2 border border-gray-300 rounded-md hover:border-primary-600 hover:text-primary-600 transition-colors duration-200\">
        <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"/>
        </svg>
      </button>
    </div>
  </div>

</article>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["attributes", "content", "url", "label"]);        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "themes/custom/juyin/templates/content/node--brand.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  166 => 64,  163 => 63,  160 => 61,  154 => 58,  151 => 57,  148 => 56,  145 => 54,  138 => 50,  134 => 48,  123 => 44,  119 => 43,  115 => 41,  112 => 40,  109 => 38,  103 => 35,  100 => 34,  97 => 33,  94 => 31,  88 => 28,  85 => 27,  82 => 26,  75 => 21,  71 => 20,  68 => 19,  64 => 16,  58 => 13,  55 => 12,  52 => 11,  47 => 8,  44 => 7,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "themes/custom/juyin/templates/content/node--brand.html.twig", "/Applications/XAMPP/xamppfiles/htdocs/drupal/themes/custom/juyin/templates/content/node--brand.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = ["if" => 11, "for" => 43];
        static $filters = ["escape" => 8];
        static $functions = ["range" => 43];

        try {
            $this->sandbox->checkSecurity(
                ['if', 'for'],
                ['escape'],
                ['range'],
                $this->source
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}

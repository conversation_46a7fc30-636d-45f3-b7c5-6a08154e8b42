<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* themes/custom/juyin/templates/content/node--product.html.twig */
class __TwigTemplate_20883769035473fb0fea51584936e01b extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->extensions[SandboxExtension::class];
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 7
        yield "
<article";
        // line 8
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["attributes"] ?? null), "addClass", ["product-card", "bg-white", "rounded-lg", "shadow-soft", "overflow-hidden", "hover:shadow-medium", "transition-shadow", "duration-300", "group"], "method", false, false, true, 8), "html", null, true);
        yield ">
  
  ";
        // line 11
        yield "  ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_images", [], "any", false, false, true, 11)) {
            // line 12
            yield "    <div class=\"product-image aspect-w-1 aspect-h-1 bg-gray-100 relative overflow-hidden\">
      ";
            // line 13
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_images", [], "any", false, false, true, 13), "html", null, true);
            yield "
      
      ";
            // line 16
            yield "      <div class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100\">
        <div class=\"flex space-x-2\">
          <button class=\"bg-white p-2 rounded-full shadow-md hover:shadow-lg transition-shadow duration-200\">
            <svg class=\"w-5 h-5 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"/>
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"/>
            </svg>
          </button>
          <button class=\"bg-white p-2 rounded-full shadow-md hover:shadow-lg transition-shadow duration-200\">
            <svg class=\"w-5 h-5 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  ";
        }
        // line 33
        yield "
  <div class=\"p-4\">
    ";
        // line 36
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_brand", [], "any", false, false, true, 36)) {
            // line 37
            yield "      <div class=\"product-brand text-xs text-gray-500 uppercase tracking-wide mb-1\">
        ";
            // line 38
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_brand", [], "any", false, false, true, 38), "html", null, true);
            yield "
      </div>
    ";
        }
        // line 41
        yield "
    ";
        // line 43
        yield "    <h3 class=\"text-lg font-semibold text-gray-900 mb-2 line-clamp-2\">
      <a href=\"";
        // line 44
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["url"] ?? null), "html", null, true);
        yield "\" class=\"hover:text-primary-600 transition-colors duration-200\">
        ";
        // line 45
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["label"] ?? null), "html", null, true);
        yield "
      </a>
    </h3>

    ";
        // line 50
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_category", [], "any", false, false, true, 50)) {
            // line 51
            yield "      <div class=\"product-category mb-2\">
        <span class=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full\">
          ";
            // line 53
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_category", [], "any", false, false, true, 53), "html", null, true);
            yield "
        </span>
      </div>
    ";
        }
        // line 57
        yield "
    ";
        // line 59
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_description", [], "any", false, false, true, 59)) {
            // line 60
            yield "      <div class=\"product-description text-gray-600 text-sm mb-3 line-clamp-2\">
        ";
            // line 61
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_description", [], "any", false, false, true, 61), "html", null, true);
            yield "
      </div>
    ";
        }
        // line 64
        yield "
    ";
        // line 66
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_rating", [], "any", false, false, true, 66)) {
            // line 67
            yield "      <div class=\"product-rating flex items-center mb-3\">
        <div class=\"flex items-center\">
          ";
            // line 69
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(range(1, 5));
            foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
                // line 70
                yield "            <svg class=\"w-3 h-3 ";
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(((($context["i"] <= CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v0 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_rating", [], "any", false, false, true, 70)) && is_array($_v0) || $_v0 instanceof ArrayAccess && in_array($_v0::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v0["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_rating", [], "any", false, false, true, 70), "#items", [], "array", false, false, true, 70)), 0, [], "any", false, false, true, 70), "value", [], "any", false, false, true, 70))) ? ("text-yellow-400") : ("text-gray-300")));
                yield "\" fill=\"currentColor\" viewBox=\"0 0 20 20\">
              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"/>
            </svg>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['i'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 74
            yield "        </div>
        <span class=\"ml-1 text-xs text-gray-500\">
          (";
            // line 76
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v1 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_rating", [], "any", false, false, true, 76)) && is_array($_v1) || $_v1 instanceof ArrayAccess && in_array($_v1::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v1["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_rating", [], "any", false, false, true, 76), "#items", [], "array", false, false, true, 76)), 0, [], "any", false, false, true, 76), "value", [], "any", false, false, true, 76), "html", null, true);
            yield ")
        </span>
      </div>
    ";
        }
        // line 80
        yield "
    ";
        // line 82
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_price", [], "any", false, false, true, 82)) {
            // line 83
            yield "      <div class=\"product-price mb-4\">
        <span class=\"text-lg font-bold text-primary-600\">
          ¥";
            // line 85
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v2 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_price", [], "any", false, false, true, 85)) && is_array($_v2) || $_v2 instanceof ArrayAccess && in_array($_v2::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v2["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_price", [], "any", false, false, true, 85), "#items", [], "array", false, false, true, 85)), 0, [], "any", false, false, true, 85), "value", [], "any", false, false, true, 85), "html", null, true);
            yield "
        </span>
        ";
            // line 87
            if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_original_price", [], "any", false, false, true, 87)) {
                // line 88
                yield "          <span class=\"text-sm text-gray-500 line-through ml-2\">
            ¥";
                // line 89
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v3 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_original_price", [], "any", false, false, true, 89)) && is_array($_v3) || $_v3 instanceof ArrayAccess && in_array($_v3::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v3["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_original_price", [], "any", false, false, true, 89), "#items", [], "array", false, false, true, 89)), 0, [], "any", false, false, true, 89), "value", [], "any", false, false, true, 89), "html", null, true);
                yield "
          </span>
        ";
            }
            // line 92
            yield "      </div>
    ";
        }
        // line 94
        yield "
    ";
        // line 96
        yield "    <div class=\"flex space-x-2\">
      <a href=\"";
        // line 97
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["url"] ?? null), "html", null, true);
        yield "\" class=\"flex-1 bg-primary-600 text-white text-center py-2 px-3 rounded-md hover:bg-primary-700 transition-colors duration-200 text-sm font-medium\">
        查看详情
      </a>
      <button class=\"compare-btn p-2 border border-gray-300 rounded-md hover:border-primary-600 hover:text-primary-600 transition-colors duration-200\">
        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"/>
        </svg>
      </button>
    </div>
  </div>

</article>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["attributes", "content", "url", "label"]);        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "themes/custom/juyin/templates/content/node--product.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  216 => 97,  213 => 96,  210 => 94,  206 => 92,  200 => 89,  197 => 88,  195 => 87,  190 => 85,  186 => 83,  183 => 82,  180 => 80,  173 => 76,  169 => 74,  158 => 70,  154 => 69,  150 => 67,  147 => 66,  144 => 64,  138 => 61,  135 => 60,  132 => 59,  129 => 57,  122 => 53,  118 => 51,  115 => 50,  108 => 45,  104 => 44,  101 => 43,  98 => 41,  92 => 38,  89 => 37,  86 => 36,  82 => 33,  63 => 16,  58 => 13,  55 => 12,  52 => 11,  47 => 8,  44 => 7,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "themes/custom/juyin/templates/content/node--product.html.twig", "/Applications/XAMPP/xamppfiles/htdocs/drupal/themes/custom/juyin/templates/content/node--product.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = ["if" => 11, "for" => 69];
        static $filters = ["escape" => 8];
        static $functions = ["range" => 69];

        try {
            $this->sandbox->checkSecurity(
                ['if', 'for'],
                ['escape'],
                ['range'],
                $this->source
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}

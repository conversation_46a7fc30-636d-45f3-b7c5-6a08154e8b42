# Chinese, Simplified translation of Token (8.x-1.15)
# Copyright (c) 2024 by the Chinese, Simplified translation team
#
msgid ""
msgstr ""
"Project-Id-Version: Token (8.x-1.15)\n"
"POT-Creation-Date: 2024-07-28 22:46+0000\n"
"PO-Revision-Date: YYYY-mm-DD HH:MM+ZZZZ\n"
"Language-Team: Chinese, Simplified\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

msgid "Title"
msgstr "标题"
msgid "Value"
msgstr "值"
msgid "Author"
msgstr "作者"
msgid "Description"
msgstr "描述"
msgid "Language"
msgstr "语言"
msgid "Content types"
msgstr "内容类型"
msgid "Number"
msgstr "数字"
msgid "Parent"
msgstr "父级"
msgid "Parents"
msgstr "父项"
msgid "Name"
msgstr "名称"
msgid "Book"
msgstr "手册"
msgid "URL"
msgstr "网址"
msgid "Path"
msgstr "路径"
msgid "Menu"
msgstr "菜单"
msgid "format"
msgstr "格式"
msgid "Content type"
msgstr "内容类型"
msgid "The name of the menu."
msgstr "请输入选单的名称。"
msgid "Menu link"
msgstr "菜单链接"
msgid "Roles"
msgstr "角色"
msgid "File size"
msgstr "文件大小"
msgid "Count"
msgstr "计数"
msgid "Width"
msgstr "宽度"
msgid "Height"
msgstr "高度"
msgid "Domain"
msgstr "域"
msgid "Last"
msgstr "末页"
msgid "Arguments"
msgstr "参数"
msgid "Random"
msgstr "随机"
msgid "Root"
msgstr "根"
msgid "Tokens"
msgstr "置换符"
msgid "Token"
msgstr "令牌(Token)"
msgid "Available tokens"
msgstr "可用的token"
msgid "First"
msgstr "第一"
msgid "Reversed"
msgstr "反向"
msgid "URI"
msgstr "URI"
msgid "Menus"
msgstr "菜单"
msgid "Language code"
msgstr "语言代码"
msgid "Extension"
msgstr "扩展名"
msgid "Keys"
msgstr "键"
msgid "Interface language"
msgstr "界面语言"
msgid "Node count"
msgstr "节点计数"
msgid "About"
msgstr "关于"
msgid "Content language"
msgstr "内容的语言"
msgid "Path prefix"
msgstr "路径前缀"
msgid "Direction"
msgstr "方向"
msgid "IP address"
msgstr "IP地址"
msgid "Root term"
msgstr "根术语"
msgid "Uses"
msgstr "使用"
msgid "MIME type"
msgstr "MIME类型"
msgid "Menu links"
msgstr "菜单链接"
msgid "Current date"
msgstr "当前日期"
msgid "Machine-readable name"
msgstr "机读名称"
msgid "Array"
msgstr "数组"
msgid "Language name"
msgstr "语言名称"
msgid "Link ID"
msgstr "链接的ID"
msgid "Edit URL"
msgstr "编辑网址"
msgid "Revision log message"
msgstr "修订日志消息"
msgid "No tokens available."
msgstr "没有可用的Token"
msgid "Click a token to insert it into the field you've last clicked."
msgstr "点击以在您最近点击的页面中插入Token"
msgid "Translation source node"
msgstr "翻译源节点"
msgid ""
"Provides a user interface for the Token API and some missing core "
"tokens."
msgstr "为替换符API和一些丢失的核心替换符提供一个用户界面。"
msgid "Insert this token into your form"
msgstr "把这个Token（令牌）插入到表单"
msgid "First click a text field to insert your tokens into."
msgstr "先点击您需要插入替换符的文字字段。"
msgid "Current page"
msgstr "当前页"
msgid "The explanation of the most recent changes made to the node."
msgstr "节点最新更改的说明。"
msgid "The title of the current page."
msgstr "当前页的标题。"
msgid "The URL of the current page."
msgstr "当前页面的URL"
msgid "The page number of the current page when viewing paged lists."
msgstr "浏览页面菜单时的当前页面数"
msgid "The source node for this current node's translation set."
msgstr "当前节点翻译集的源节点"
msgid "Account cancellation URL"
msgstr "账户撤销URL"
msgid "The URL of the confirm delete page for the user account."
msgstr "用户账号确定删除页面的URL"
msgid "One-time login URL"
msgstr "单次登陆的URL"
msgid "Tokens related to menu links."
msgstr "与菜单链接相关的Token(令牌)。"
msgid "The unique ID of the menu link."
msgstr "菜单链接的唯一ID。"
msgid "The title of the menu link."
msgstr "菜单链接的标题。"
msgid "The URL of the menu link."
msgstr "菜单链接的URL。"
msgid "The menu link's parent."
msgstr "父级菜单链接。"
msgid "Tokens related to the current page request."
msgstr "与当前页请求相关的token。"
msgid "Page number"
msgstr "页面数"
msgid "The menu link for this node."
msgstr "此节点的菜单链接。"
msgid "Hash"
msgstr "哈希"
msgid "The extension of the file."
msgstr "文件的扩展名"
msgid "Tokens related to the current date and time."
msgstr "与当前日期和时间相关的Token（令牌）。"
msgid "The URL of the @entity."
msgstr "@entity 的URL。"
msgid "The content type of the node."
msgstr "节点的内容类型。"
msgid "Tokens related to content types."
msgstr "与内容类型相关的Token（令牌）。"
msgid "The name of the content type."
msgstr "内容类型的名称"
msgid "The unique machine-readable name of the content type."
msgstr "内容类型的唯一机读名称。"
msgid "The optional description of the content type."
msgstr "内容类型的可选描述。"
msgid "The number of nodes belonging to the content type."
msgstr "属于这个内容类型的节点数量。"
msgid "The URL of the content type's edit page."
msgstr "节点编辑页面的URL。"
msgid "The URL of the taxonomy term's edit page."
msgstr "分类属于编辑页面的网址。"
msgid "The unique machine-readable name of the vocabulary."
msgstr "词汇的唯一机读名字。"
msgid "The URL of the vocabulary's edit page."
msgstr "词汇编辑页面的URL。"
msgid ""
"The specific argument of the current page (e.g. 'arg:1' on the page "
"'node/1' returns '1')."
msgstr ""
"当前页面的具体参数 (例如： 页面'node/1'的'arg:1' 返回 "
"'1')。"
msgid "Tokens related to URLs."
msgstr "与网址相关的Token（令牌）。"
msgid "Relative URL"
msgstr "相对URL"
msgid "The relative URL."
msgstr "相对URL。"
msgid "Absolute URL"
msgstr "绝对URL"
msgid "The absolute URL."
msgstr "绝对URL。"
msgid "Tokens related to menus."
msgstr "与菜单相关的Token(令牌)。"
msgid "The unique machine-readable name of the menu."
msgstr "菜单的唯一机器可读名称。"
msgid "The optional description of the menu."
msgstr "菜单的可选择的描述。"
msgid "Menu link count"
msgstr "菜单链接的数量"
msgid "The number of menu links belonging to the menu."
msgstr "属于菜单的菜单链接的数量。"
msgid "The URL of the menu's edit page."
msgstr "菜单编辑页面的URL。"
msgid "The menu of the menu link."
msgstr "菜单链接的菜单。"
msgid "The URL of the menu link's edit page."
msgstr "菜单链接编辑页面的网址。"
msgid "The user roles associated with the user account."
msgstr "与用户账号相关联的的用户角色。"
msgid "Brief URL"
msgstr "简短URL。"
msgid "The URL without the protocol and trailing backslash."
msgstr "不带协议和结尾反斜线（/）的URL。"
msgid "Tokens related to arrays of strings."
msgstr "字符串数组相关的Token。"
msgid "The first element of the array."
msgstr "数组的第一个元素。"
msgid "The last element of the array."
msgstr "数组的最后一个元素。"
msgid "The number of elements in the array."
msgstr "数组中元素的数量。"
msgid "The array reversed."
msgstr "数组已反转。"
msgid "The array of keys of the array."
msgstr "数组键值的数组。"
msgid "A date in '@type' format. (%date)"
msgstr "一个格式是 '@type' 的日期。 (%date)"
msgid "The root term of the taxonomy term."
msgstr "分类术语的根术语。"
msgid "File byte size"
msgstr "文件的字节大小"
msgid "The size of the file, in bytes."
msgstr "文件的大小，单位为字节"
msgid "Query string value"
msgstr "查询字符串值"
msgid "A random number from 0 to @max."
msgstr "一个从 0 到 @max 的随机数。"
msgid "A random hash. The possible hashing algorithms are: @hash-algos."
msgstr "一个随机的哈希。可能的算法为: @hash-algos。"
msgid "@type field."
msgstr "@type 字段。"
msgid "No tokens available"
msgstr "无可用的token(令牌)"
msgid "Unaliased URL"
msgstr "无别名的URL"
msgid "The unaliased URL."
msgstr "无别名的URL。"
msgid "An array of all the term's parents, starting with the root."
msgstr "由根开始的条目数组"
msgid "An array of all the menu link's parents, starting with the root."
msgstr "从根开始的目录链接数组"
msgid "Original @entity"
msgstr "源 @entity"
msgid "This field supports tokens."
msgstr "此字段支持置换符（token）。"
msgid "List of @type values"
msgstr "@type 值的列表"
msgid "Browse available tokens."
msgstr "浏览可用的token(令牌)"
msgid ""
"Available variables are: [site:name], [site:url], [user:display-name], "
"[user:account-name], [user:mail], [site:login-url], [site:url-brief], "
"[user:edit-url], [user:one-time-login-url], [user:cancel-url]."
msgstr ""
"可用的通配符： [site:name], [site:url], [user:display-name], "
"[user:account-name], [user:mail], [site:login-url], [site:url-brief], "
"[user:edit-url], [user:one-time-login-url], [user:cancel-url]."
msgid "The language code."
msgstr "语言代码。"
msgid "Token or token types are defined by multiple modules"
msgstr "令牌或令牌类型是由多个模块定义的"

# Chinese, Simplified translation of Pathauto (8.x-1.13)
# Copyright (c) 2024 by the Chinese, Simplified translation team
#
msgid ""
msgstr ""
"Project-Id-Version: Pathauto (8.x-1.13)\n"
"POT-Creation-Date: 2024-08-01 10:46+0000\n"
"PO-Revision-Date: YYYY-mm-DD HH:MM+ZZZZ\n"
"Language-Team: Chinese, Simplified\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

msgid "Remove"
msgstr "移除"
msgid "Language"
msgstr "语言"
msgid "Enable"
msgstr "启用"
msgid "Disable"
msgstr "禁用"
msgid "Enabled"
msgstr "启用"
msgid "Weight"
msgstr "权重"
msgid "Settings"
msgstr "设置"
msgid "Label"
msgstr "标签"
msgid "Update"
msgstr "更新"
msgid "ID"
msgstr "ID"
msgid "Separator"
msgstr "分隔符"
msgid "URL alias"
msgstr "URL别名"
msgid "Conditions"
msgstr "条件"
msgid "Verbose"
msgstr "详尽模式"
msgid "Display alias changes (except during bulk updates)."
msgstr "显示别名变更（除了在批量更新之间）。"
msgid "Replace by separator"
msgstr "用分隔符替代"
msgid "Maximum alias length"
msgstr "别名最长值"
msgid "Maximum component length"
msgstr "最大组件长度"
msgid "Update action"
msgstr "更新操作"
msgid "Strings to Remove"
msgstr "要移除的字符串"
msgid "Pathauto"
msgstr "Pathauto"
msgid ""
"Provides a mechanism for modules to automatically generate aliases for "
"the content they manage."
msgstr "给模块们所管理的内容提供自动创建URL路径别名的功能。"
msgid "Forum"
msgstr "论坛"
msgid "Pattern"
msgstr "模式"
msgid "Languages"
msgstr "语言"
msgid "Period"
msgstr "句号"
msgid "Asterisk"
msgstr "星号"
msgid "Duplicate"
msgstr "复制"
msgid "Relationship"
msgstr "关联"
msgid ""
"The automatically generated alias %original_alias conflicted with an "
"existing alias. Alias changed to %alias."
msgstr ""
"自动创建的别名 %original_alias "
"与现有别名冲突。别名已更改为 %alias。"
msgid "Delete aliases"
msgstr "删除别名"
msgid "Automatic alias"
msgstr "自动别名"
msgid ""
"Character used to separate words in titles. This will replace any "
"spaces and punctuation characters. Using a space or + character can "
"cause unexpected results."
msgstr ""
"标题中用于分隔单词的字符。这么做会替换所有空格符和标点符号。使用空格符或者 "
"+ 符号有可能导致无法预料的后果。"
msgid "Character case"
msgstr "字符大小写"
msgid "Do nothing. Leave the old alias intact."
msgstr "什么都不做。保持原别名不变。"
msgid "Create a new alias. Leave the existing alias functioning."
msgstr "创建新别名。使现有别名仍然有效。"
msgid "Create a new alias. Delete the old alias."
msgstr "删除原别名，创建新别名。"
msgid "Transliterate prior to creating alias"
msgstr "创建别名之前先进行音译"
msgid ""
"Filters the new alias to only letters and numbers found in the "
"ASCII-96 set."
msgstr "用ASCII-96字符集中仅有的字母和数字来过滤新别名。"
msgid "No action (do not replace)"
msgstr "无操作 (不替换)"
msgid "Delete all aliases. Number of aliases which will be deleted: %count."
msgstr "删除所有别名。将删除的别名数: %count。"
msgid ""
"Delete aliases for all @label. Number of aliases which will be "
"deleted: %count."
msgstr "删除所有 @label 的别名。将删除的别名数: %count。"
msgid "Delete aliases now!"
msgstr "立即删除别名!"
msgid "All of your path aliases have been deleted."
msgstr "已删除所有路径别名。"
msgid "About"
msgstr "关于"
msgid "Patterns"
msgstr "模式"
msgid "Colon"
msgstr "冒号"
msgid "Comma"
msgstr "逗号(,)"
msgid "Semicolon"
msgstr "分号(;)"
msgid "Uses"
msgstr "使用"
msgid "Slash"
msgstr "右斜线"
msgid "Alias: @alias"
msgstr "别名：@alias"
msgid "No alias"
msgstr "无别名"
msgid "Punctuation"
msgstr "标点符号"
msgid "Broken"
msgstr "损坏"
msgid "Ampersand"
msgstr "与标记(&)"
msgid ""
"What should Pathauto do when updating an existing content item which "
"already has an alias?"
msgstr "如果修改已有内容时，对已经有的别名如何处理？"
msgid "Reduce strings to letters and numbers"
msgstr "只用字母和数字"
msgid ""
"Words to strip out of the URL alias, separated by commas. Do not use "
"this to remove punctuation."
msgstr "在别名生成过程中，需要去掉的文字。（不用管标点符号）"
msgid "Choose aliases to delete"
msgstr "选择需要删除的别名"
msgid "All aliases"
msgstr "所有别名"
msgid "Ignoring alias %alias because it is the same as the internal path."
msgstr "与系统内部路径冲突，忽略别名%alias"
msgid "Created new alias %alias for %source, replacing %old_alias."
msgstr "为%source生成新的别名%alias替代以前的别名%old_alias。"
msgid "Created new alias %alias for %source."
msgstr "为%source生成新的别名%alias"
msgid "Administer pathauto"
msgstr "管理Pathauto"
msgid "Notify of Path Changes"
msgstr "告知路径变化"
msgid "Determines whether or not users are notified."
msgstr "决定是否通知用户"
msgid "Bulk updating URL aliases"
msgstr "批量更新链接别名"
msgid "An error occurred while processing @operation with arguments : @args"
msgstr "当处理带有参数@args的操作@operation时产生一个错误"
msgid "Generated 1 URL alias."
msgid_plural "Generated @count URL aliases."
msgstr[0] "已生成1个URL别名。"
msgstr[1] "已生成@count个URL别名。"
msgid "Update URL alias"
msgstr "更新URL别名"
msgid "Underscore"
msgstr "下划线"
msgid "No new URL aliases to generate."
msgstr "没有生成新的链接别名"
msgid "Double quotation marks"
msgstr "双引号"
msgid "Single quotation marks (apostrophe)"
msgstr "单引号"
msgid "Back tick"
msgstr "反引号"
msgid "Hyphen"
msgstr "连字符"
msgid "Vertical bar (pipe)"
msgstr "竖线"
msgid "Left curly bracket"
msgstr "左括号"
msgid "Left square bracket"
msgstr "左方括号"
msgid "Right curly bracket"
msgstr "右括号"
msgid "Right square bracket"
msgstr "右方括号"
msgid "Plus sign"
msgstr "加号"
msgid "Equal sign"
msgstr "等号"
msgid "Percent sign"
msgstr "百分号"
msgid "Caret"
msgstr "脱字符号"
msgid "Dollar sign"
msgstr "美元符号"
msgid "Number sign (pound sign, hash)"
msgstr "井号"
msgid "At sign"
msgstr "@"
msgid "Exclamation mark"
msgstr "感叹号!"
msgid "Tilde"
msgstr "波浪号"
msgid "Left parenthesis"
msgstr "左括号"
msgid "Right parenthesis"
msgstr "右括号"
msgid "Question mark"
msgstr "问号"
msgid "Less-than sign"
msgstr "小于号"
msgid "Greater-than sign"
msgstr "大于号"
msgid "Backslash"
msgstr "左斜线"
msgid "Generate automatic URL alias"
msgstr "自动生成链接别名"
msgid "Uncheck this to create a custom alias below."
msgstr "取消选中,可以在下面创建一个自定义的别名。"
msgid "Bulk generate"
msgstr "批量生成"
msgid "Broken/Missing"
msgstr "断开/丢失"

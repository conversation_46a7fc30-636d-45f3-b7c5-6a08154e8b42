!function(e){const t=e.uz=e.uz||{};t.dictionary=Object.assign(t.dictionary||{},{"Advanced options":"",Find:"","Find and replace":"","Find in text…":"","Find in the document":"","Match case":"","Next result":"","Previous result":"",Replace:"","Replace all":"","Replace with…":"","Text to find must not be empty.":"","Tip: Find some text first in order to replace it.":"","Whole words only":""})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));
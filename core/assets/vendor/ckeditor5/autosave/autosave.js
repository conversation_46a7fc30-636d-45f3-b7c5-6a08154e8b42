!function(t){const e=t.en=t.en||{};e.dictionary=Object.assign(e.dictionary||{},{"Saving changes":"Saving changes"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={782:(t,e,i)=>{t.exports=i(237)("./src/core.js")},584:(t,e,i)=>{t.exports=i(237)("./src/utils.js")},237:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function i(n){var s=e[n];if(void 0!==s)return s.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,i),o.exports}i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};(()=>{"use strict";i.r(n),i.d(n,{Autosave:()=>D});var t=i(782),e=i(584);const s=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)};const o="object"==typeof global&&global&&global.Object===Object&&global;var r="object"==typeof self&&self&&self.Object===Object&&self;const a=o||r||Function("return this")();const c=function(){return a.Date.now()};var u=/\s/;const l=function(t){for(var e=t.length;e--&&u.test(t.charAt(e)););return e};var d=/^\s+/;const h=function(t){return t?t.slice(0,l(t)+1).replace(d,""):t};const v=a.Symbol;var f=Object.prototype,m=f.hasOwnProperty,g=f.toString,_=v?v.toStringTag:void 0;const p=function(t){var e=m.call(t,_),i=t[_];try{t[_]=void 0;var n=!0}catch(t){}var s=g.call(t);return n&&(e?t[_]=i:delete t[_]),s};var b=Object.prototype.toString;const y=function(t){return b.call(t)};var S=v?v.toStringTag:void 0;const T=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":S&&S in Object(t)?p(t):y(t)};const j=function(t){return null!=t&&"object"==typeof t};const w=function(t){return"symbol"==typeof t||j(t)&&"[object Symbol]"==T(t)};var O=/^[-+]0x[0-9a-f]+$/i,P=/^0b[01]+$/i,A=/^0o[0-7]+$/i,x=parseInt;const E=function(t){if("number"==typeof t)return t;if(w(t))return NaN;if(s(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=s(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=h(t);var i=P.test(t);return i||A.test(t)?x(t.slice(2),i?2:8):O.test(t)?NaN:+t};var N=Math.max,I=Math.min;const C=function(t,e,i){var n,o,r,a,u,l,d=0,h=!1,v=!1,f=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function m(e){var i=n,s=o;return n=o=void 0,d=e,a=t.apply(s,i)}function g(t){var i=t-l;return void 0===l||i>=e||i<0||v&&t-d>=r}function _(){var t=c();if(g(t))return p(t);u=setTimeout(_,function(t){var i=e-(t-l);return v?I(i,r-(t-d)):i}(t))}function p(t){return u=void 0,f&&n?m(t):(n=o=void 0,a)}function b(){var t=c(),i=g(t);if(n=arguments,o=this,l=t,i){if(void 0===u)return function(t){return d=t,u=setTimeout(_,e),h?m(t):a}(l);if(v)return clearTimeout(u),u=setTimeout(_,e),m(l)}return void 0===u&&(u=setTimeout(_,e)),a}return e=E(e)||0,s(i)&&(h=!!i.leading,r=(v="maxWait"in i)?N(E(i.maxWait)||0,e):r,f="trailing"in i?!!i.trailing:f),b.cancel=function(){void 0!==u&&clearTimeout(u),d=0,n=l=o=u=void 0},b.flush=function(){return void 0===u?a:p(c())},b};class D extends t.Plugin{static get pluginName(){return"Autosave"}static get isOfficialPlugin(){return!0}static get requires(){return[t.PendingActions]}constructor(i){super(i),this._action=null;const n=i.config.get("autosave")||{},s=n.waitingTime||1e3;this.set("state","synchronized"),this._debouncedSave=C(this._save.bind(this),s),this._lastDocumentVersion=i.model.document.version,this._savePromise=null,this._domEmitter=new((0,e.DomEmitterMixin)()),this._config=n,this._pendingActions=i.plugins.get(t.PendingActions),this._makeImmediateSave=!1}init(){const t=this.editor,e=t.model.document;this.listenTo(t,"ready",(()=>{this.listenTo(e,"change:data",((t,e)=>{this._saveCallbacks.length&&e.isLocal&&("synchronized"===this.state&&(this.state="waiting",this._setPendingAction()),"waiting"===this.state&&this._debouncedSave())}))})),this.listenTo(t,"destroy",(()=>this._flush()),{priority:"highest"}),this._domEmitter.listenTo(window,"beforeunload",((t,e)=>{this._pendingActions.hasAny&&(e.returnValue=this._pendingActions.first.message)}))}destroy(){this._domEmitter.stopListening(),super.destroy()}save(){return this._debouncedSave.cancel(),this._save()}_flush(){this._debouncedSave.flush()}_save(){return this._savePromise?(this._makeImmediateSave=this.editor.model.document.version>this._lastDocumentVersion,this._savePromise):(this._setPendingAction(),this.state="saving",this._lastDocumentVersion=this.editor.model.document.version,this._savePromise=Promise.resolve().then((()=>Promise.all(this._saveCallbacks.map((t=>t(this.editor)))))).finally((()=>{this._savePromise=null})).then((()=>{if(this._makeImmediateSave)return this._makeImmediateSave=!1,this._save();this.editor.model.document.version>this._lastDocumentVersion?(this.state="waiting",this._debouncedSave()):(this.state="synchronized",this._pendingActions.remove(this._action),this._action=null)})).catch((t=>{throw this.state="error",this.state="saving",this._debouncedSave(),t})),this._savePromise)}_setPendingAction(){const t=this.editor.t;this._action||(this._action=this._pendingActions.add(t("Saving changes")))}get _saveCallbacks(){const t=[];return this.adapter&&this.adapter.save&&t.push(this.adapter.save),this._config.save&&t.push(this._config.save),t}}})(),(window.CKEditor5=window.CKEditor5||{}).autosave=n})();
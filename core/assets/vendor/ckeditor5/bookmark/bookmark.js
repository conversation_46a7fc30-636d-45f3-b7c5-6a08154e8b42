!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{Bookmark:"Bookmark","Bookmark must not be empty.":"Bookmark must not be empty.","Bookmark name":"Bookmark name","Bookmark name already exists.":"Bookmark name already exists.","Bookmark name cannot contain space characters.":"Bookmark name cannot contain space characters.","bookmark widget":"bookmark widget","Edit bookmark":"Edit bookmark","Enter the bookmark name without spaces.":"Enter the bookmark name without spaces.",Insert:"Insert","Remove bookmark":"Remove bookmark",Update:"Update"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={501:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var i=o(758),r=o.n(i),s=o(935),n=o.n(s)()(r());n.push([e.id,'.ck-vertical-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck-vertical-form .ck-button:focus:after{display:none}@media screen and (max-width:600px){.ck.ck-responsive-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck.ck-responsive-form .ck-button:focus:after{display:none}}.ck-vertical-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form{padding:var(--ck-spacing-large)}.ck.ck-responsive-form:focus{outline:none}[dir=ltr] .ck.ck-responsive-form>:not(:first-child),[dir=rtl] .ck.ck-responsive-form>:not(:last-child){margin-left:var(--ck-spacing-standard)}@media screen and (max-width:600px){.ck.ck-responsive-form{padding:0;width:calc(var(--ck-input-width)*.8)}.ck.ck-responsive-form .ck-labeled-field-view{margin:var(--ck-spacing-large) var(--ck-spacing-large) 0}.ck.ck-responsive-form .ck-labeled-field-view .ck-input-number,.ck.ck-responsive-form .ck-labeled-field-view .ck-input-text{min-width:0;width:100%}.ck.ck-responsive-form .ck-labeled-field-view .ck-labeled-field-view__error{white-space:normal}.ck.ck-responsive-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form>.ck-button:last-child,.ck.ck-responsive-form>.ck-button:nth-last-child(2){border-radius:0;margin-top:var(--ck-spacing-large);padding:var(--ck-spacing-standard)}.ck.ck-responsive-form>.ck-button:last-child:not(:focus),.ck.ck-responsive-form>.ck-button:nth-last-child(2):not(:focus){border-top:1px solid var(--ck-color-base-border)}[dir=ltr] .ck.ck-responsive-form>.ck-button:last-child,[dir=ltr] .ck.ck-responsive-form>.ck-button:nth-last-child(2),[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2){margin-left:0}[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child:last-of-type,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2):last-of-type{border-right:1px solid var(--ck-color-base-border)}}',""]);const a=n},493:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var i=o(758),r=o.n(i),s=o(935),n=o.n(s)()(r());n.push([e.id,":root{--ck-bookmark-icon-hover-fill-color:var(--ck-color-widget-hover-border);--ck-bookmark-icon-selected-fill-color:var(--ck-color-focus-border);--ck-bookmark-icon-animation-duration:var(--ck-widget-handler-animation-duration);--ck-bookmark-icon-animation-curve:var(--ck-widget-handler-animation-curve)}.ck-bookmark.ck-widget{outline:none}.ck-bookmark.ck-widget .ck-bookmark__icon .ck-icon__fill{transition:fill var(--ck-bookmark-icon-animation-duration) var(--ck-bookmark-icon-animation-curve)}.ck-bookmark.ck-widget:hover .ck-bookmark__icon .ck-icon__fill{fill:var(--ck-bookmark-icon-hover-fill-color)}.ck-bookmark.ck-widget.ck-widget_selected .ck-bookmark__icon .ck-icon__fill{fill:var(--ck-bookmark-icon-selected-fill-color)}.ck-bookmark.ck-widget.ck-widget_selected,.ck-bookmark.ck-widget.ck-widget_selected:hover{outline:none}.ck-bookmark.ck-widget .ck-bookmark__icon{position:relative;top:-.1em}.ck-bookmark.ck-widget .ck-bookmark__icon .ck-icon{height:1.2em;vertical-align:middle;width:auto}.ck .ck-fake-bookmark-selection{background:var(--ck-color-link-fake-selection)}.ck .ck-fake-bookmark-selection_collapsed{border-right:1px solid var(--ck-color-base-text);height:100%;margin-right:-1px;outline:1px solid hsla(0,0%,100%,.5)}",""]);const a=n},324:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var i=o(758),r=o.n(i),s=o(935),n=o.n(s)()(r());n.push([e.id,".ck.ck-bookmark-actions{align-items:center;display:flex}.ck.ck-bookmark-actions .ck-bookmark-actions__preview{cursor:default;font-weight:400;max-width:var(--ck-input-width);min-width:3em;overflow:hidden;text-align:center;text-overflow:ellipsis;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;user-select:none}@media screen and (max-width:600px){.ck.ck-bookmark-actions{display:flex;flex-wrap:wrap}.ck.ck-bookmark-actions .ck-bookmark-actions__preview{flex-basis:100%;margin:var(--ck-spacing-standard) var(--ck-spacing-standard) 0;min-width:auto}.ck.ck-bookmark-actions.ck-responsive-form .ck-button{flex-basis:50%;margin-top:var(--ck-spacing-standard)}}",""]);const a=n},731:(e,t,o)=>{"use strict";o.d(t,{A:()=>a});var i=o(758),r=o.n(i),s=o(935),n=o.n(s)()(r());n.push([e.id,".ck.ck-bookmark-view:focus{outline:none}.ck.ck-bookmark-form{align-items:flex-start;display:flex}@media screen and (max-width:600px){.ck.ck-bookmark-form{flex-wrap:wrap}.ck.ck-bookmark-form .ck-button,.ck.ck-bookmark-form .ck-labeled-field-view{flex-basis:100%}.ck.ck-bookmark-form .ck-button{justify-content:center}.ck.ck-bookmark-form.ck-responsive-form>.ck-button:last-child{border-radius:var(--ck-border-radius);margin:var(--ck-spacing-large);padding:0 var(--ck-spacing-standard)}}",""]);const a=n},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var o="",i=void 0!==t[5];return t[4]&&(o+="@supports (".concat(t[4],") {")),t[2]&&(o+="@media ".concat(t[2]," {")),i&&(o+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),o+=e(t),i&&(o+="}"),t[2]&&(o+="}"),t[4]&&(o+="}"),o})).join("")},t.i=function(e,o,i,r,s){"string"==typeof e&&(e=[[null,e,void 0]]);var n={};if(i)for(var a=0;a<this.length;a++){var c=this[a][0];null!=c&&(n[c]=!0)}for(var l=0;l<e.length;l++){var d=[].concat(e[l]);i&&n[d[0]]||(void 0!==s&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=s),o&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=o):d[2]=o),r&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=r):d[4]="".concat(r)),t.push(d))}},t}},758:e=>{"use strict";e.exports=function(e){return e[1]}},591:e=>{"use strict";var t=[];function o(e){for(var o=-1,i=0;i<t.length;i++)if(t[i].identifier===e){o=i;break}return o}function i(e,i){for(var s={},n=[],a=0;a<e.length;a++){var c=e[a],l=i.base?c[0]+i.base:c[0],d=s[l]||0,k="".concat(l," ").concat(d);s[l]=d+1;var m=o(k),u={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==m)t[m].references++,t[m].updater(u);else{var h=r(u,i);i.byIndex=a,t.splice(a,0,{identifier:k,updater:h,references:1})}n.push(k)}return n}function r(e,t){var o=t.domAPI(t);o.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;o.update(e=t)}else o.remove()}}e.exports=function(e,r){var s=i(e=e||[],r=r||{});return function(e){e=e||[];for(var n=0;n<s.length;n++){var a=o(s[n]);t[a].references--}for(var c=i(e,r),l=0;l<s.length;l++){var d=o(s[l]);0===t[d].references&&(t[d].updater(),t.splice(d,1))}s=c}}},128:e=>{"use strict";var t={};e.exports=function(e,o){var i=function(e){if(void 0===t[e]){var o=document.querySelector(e);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}t[e]=o}return t[e]}(e);if(!i)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");i.appendChild(o)}},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(o){e.setAttribute(o,t[o])}))}},639:e=>{"use strict";var t,o=(t=[],function(e,o){return t[e]=o,t.filter(Boolean).join("\n")});function i(e,t,i,r){var s;if(i)s="";else{s="",r.supports&&(s+="@supports (".concat(r.supports,") {")),r.media&&(s+="@media ".concat(r.media," {"));var n=void 0!==r.layer;n&&(s+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),s+=r.css,n&&(s+="}"),r.media&&(s+="}"),r.supports&&(s+="}")}if(e.styleSheet)e.styleSheet.cssText=o(t,s);else{var a=document.createTextNode(s),c=e.childNodes;c[t]&&e.removeChild(c[t]),c.length?e.insertBefore(a,c[t]):e.appendChild(a)}}var r={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=r.singletonCounter++,o=r.singleton||(r.singleton=e.insertStyleElement(e));return{update:function(e){i(o,t,!1,e)},remove:function(e){i(o,t,!0,e)}}}},782:(e,t,o)=>{e.exports=o(237)("./src/core.js")},783:(e,t,o)=>{e.exports=o(237)("./src/engine.js")},311:(e,t,o)=>{e.exports=o(237)("./src/ui.js")},584:(e,t,o)=>{e.exports=o(237)("./src/utils.js")},901:(e,t,o)=>{e.exports=o(237)("./src/widget.js")},237:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function o(i){var r=t[i];if(void 0!==r)return r.exports;var s=t[i]={id:i,exports:{}};return e[i](s,s.exports,o),s.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var i in t)o.o(t,i)&&!o.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{"use strict";o.r(i),o.d(i,{Bookmark:()=>M,BookmarkEditing:()=>F,BookmarkUI:()=>O,InsertBookmarkCommand:()=>x,UpdateBookmarkCommand:()=>A});var e=o(782),t=o(901),r=o(311),s=o(783),n=o(584),a=o(591),c=o.n(a),l=o(639),d=o.n(l),k=o(128),m=o.n(k),u=o(21),h=o.n(u),b=o(51),f=o.n(b),p=o(501),w={attributes:{"data-cke":!0}};w.setAttributes=h(),w.insert=m().bind(null,"head"),w.domAPI=d(),w.insertStyleElement=f();c()(p.A,w);p.A&&p.A.locals&&p.A.locals;var v=o(731),g={attributes:{"data-cke":!0}};g.setAttributes=h(),g.insert=m().bind(null,"head"),g.domAPI=d(),g.insertStyleElement=f();c()(v.A,g);v.A&&v.A.locals&&v.A.locals;class _ extends r.View{constructor(e,t){super(e),this.focusTracker=new n.FocusTracker,this.keystrokes=new n.KeystrokeHandler,this._focusables=new r.ViewCollection;const o=e.t;this._validators=t,this.idInputView=this._createIdInput(),this.buttonView=this._createButton(o("Insert"),"ck-button-action ck-button-bold"),this.buttonView.type="submit",this.children=this._createViewChildren(),this._focusCycler=new r.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}});this.setTemplate({tag:"form",attributes:{class:["ck","ck-bookmark-view"],tabindex:"-1"},children:this.children})}render(){super.render(),(0,r.submitHandler)({view:this});[this.idInputView,this.buttonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)})),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}isValid(){this.resetFormStatus();for(const e of this._validators){const t=e(this);if(t)return this.idInputView.errorText=t,!1}return!0}resetFormStatus(){this.idInputView.errorText=null}_createViewChildren(){const e=this.createCollection(),t=this.t;return e.add(new r.FormHeaderView(this.locale,{label:t("Bookmark")})),e.add(this._createFormContentView()),e}_createFormContentView(){const e=new r.View(this.locale),t=this.createCollection();return t.add(this.idInputView),t.add(this.buttonView),e.setTemplate({tag:"div",attributes:{class:["ck","ck-bookmark-form","ck-responsive-form"]},children:t}),e}_createIdInput(){const e=this.locale.t,t=new r.LabeledFieldView(this.locale,r.createLabeledInputText);return t.label=e("Bookmark name"),t.infoText=e("Enter the bookmark name without spaces."),t}_createButton(e,t){const o=new r.ButtonView(this.locale);return o.set({label:e,withText:!0}),o.extendTemplate({attributes:{class:t}}),o}get id(){const{element:e}=this.idInputView.fieldView;return e?e.value.trim():null}}var V=o(324),y={attributes:{"data-cke":!0}};y.setAttributes=h(),y.insert=m().bind(null,"head"),y.domAPI=d(),y.insertStyleElement=f();c()(V.A,y);V.A&&V.A.locals&&V.A.locals;class B extends r.View{constructor(t){super(t),this.focusTracker=new n.FocusTracker,this.keystrokes=new n.KeystrokeHandler,this._focusables=new r.ViewCollection;const o=t.t;this.bookmarkPreviewView=this._createBookmarkPreviewView(),this.removeButtonView=this._createButton(o("Remove bookmark"),e.icons.remove,"remove",this.bookmarkPreviewView),this.editButtonView=this._createButton(o("Edit bookmark"),e.icons.pencil,"edit",this.bookmarkPreviewView),this.set("id",void 0),this._focusCycler=new r.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"div",attributes:{class:["ck","ck-bookmark-actions","ck-responsive-form"],tabindex:"-1"},children:[this.bookmarkPreviewView,this.editButtonView,this.removeButtonView]})}render(){super.render();[this.editButtonView,this.removeButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)})),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}_createButton(e,t,o,i){const s=new r.ButtonView(this.locale);return s.set({label:e,icon:t,tooltip:!0}),s.delegate("execute").to(this,o),s.labelView.unbind("id"),s.labelView.bind("id").to(s,"ariaLabelledBy",(e=>e.split(" ")[0])),s.ariaLabelledBy=`${s.ariaLabelledBy} ${i.id}`,s}_createBookmarkPreviewView(){const e=new r.LabelView(this.locale);return e.extendTemplate({attributes:{class:["ck","ck-bookmark-actions__preview"]}}),e.bind("text").to(this,"id"),e}}function I(e){return!(!e||"string"!=typeof e)&&!/\s/.test(e)}class x extends e.Command{refresh(){const e=this.editor.model.document.selection,t=this._getPositionToInsertBookmark(e);this.isEnabled=!!t}execute(e){if(!e)return;const{bookmarkId:t}=e;if(!I(t))return void(0,n.logWarning)("insert-bookmark-command-executed-with-invalid-name");const o=this.editor,i=o.model,r=i.document.selection;i.change((e=>{let s=this._getPositionToInsertBookmark(r);if(!i.schema.checkChild(s,"bookmark")){const e=o.execute("insertParagraph",{position:s});if(!e)return;s=e}const n=e.createElement("bookmark",{...Object.fromEntries(r.getAttributes()),bookmarkId:t});i.insertObject(n,s,null,{setSelection:"on"})}))}_getPositionToInsertBookmark(e){const t=this.editor.model,o=t.schema,i=e.getFirstRange(),r=i.start;if(E(r,o))return r;for(const{previousPosition:e,item:r}of i){if(r.is("element")&&o.checkChild(r,"$text")&&E(r,o))return t.createPositionAt(r,0);if(E(e,o))return e}return null}}function E(e,t){return!!t.checkChild(e,"bookmark")||!!t.checkChild(e,"paragraph")&&t.checkChild("paragraph","bookmark")}class A extends e.Command{refresh(){const e=T(this.editor.model.document.selection);this.isEnabled=!!e,this.value=e?e.getAttribute("bookmarkId"):void 0}execute(e){if(!e)return;const{bookmarkId:t}=e;if(!I(t))return void(0,n.logWarning)("update-bookmark-command-executed-with-invalid-name");const o=this.editor.model,i=T(o.document.selection);i&&o.change((e=>{e.setAttribute("bookmarkId",t,i)}))}}function T(e){const t=e.getSelectedElement();return t&&t.is("element","bookmark")?t:null}var C=o(493),P={attributes:{"data-cke":!0}};P.setAttributes=h(),P.insert=m().bind(null,"head"),P.domAPI=d(),P.insertStyleElement=f();c()(C.A,P);C.A&&C.A.locals&&C.A.locals;class F extends e.Plugin{constructor(){super(...arguments),this._bookmarkElements=new Map}static get pluginName(){return"BookmarkEditing"}static get isOfficialPlugin(){return!0}init(){const{editor:e}=this;this._defineSchema(),this._defineConverters(),e.commands.add("insertBookmark",new x(e)),e.commands.add("updateBookmark",new A(e)),this.listenTo(e.model.document,"change:data",(()=>{this._trackBookmarkElements()}))}getElementForBookmarkId(e){for(const[t,o]of this._bookmarkElements)if(o==e)return t;return null}_defineSchema(){this.editor.model.schema.register("bookmark",{inheritAllFrom:"$inlineObject",allowAttributes:"bookmarkId",disallowAttributes:["linkHref","htmlA"]})}_defineConverters(){const{editor:e}=this,{conversion:o,t:i}=e;e.data.htmlProcessor.domConverter.registerInlineObjectMatcher((e=>S(e))),e.editing.view.domConverter.registerInlineObjectMatcher((e=>S(e,!1))),o.for("dataDowncast").elementToElement({model:{name:"bookmark",attributes:["bookmarkId"]},view:(e,{writer:t})=>t.createEmptyElement("a",{id:e.getAttribute("bookmarkId")})}),o.for("editingDowncast").elementToElement({model:{name:"bookmark",attributes:["bookmarkId"]},view:(e,{writer:o})=>{const r=e.getAttribute("bookmarkId"),s=o.createContainerElement("a",{id:r,class:"ck-bookmark"},[this._createBookmarkUIElement(o)]);this._bookmarkElements.set(e,r);return(0,t.toWidget)(s,o,{label:()=>`${r} ${i("bookmark widget")}`})}}),o.for("upcast").add((t=>t.on("element:a",function(e){return(t,o,i)=>{const r=o.viewItem,s=S(r,!1);if(!s||!i.consumable.test(r,s))return;const n=function(e){const t=e.config.get("bookmark.enableNonEmptyAnchorConversion");return void 0===t||t}(e);if(!n&&!r.isEmpty)return;const a=i.writer,c=r.getAttribute("id"),l=r.getAttribute("name"),d=c||l,k=a.createElement("bookmark",{bookmarkId:d});if(!i.safeInsert(k,o.modelCursor))return;i.consumable.consume(r,s),c===l&&i.consumable.consume(r,{attributes:["name"]}),i.updateConversionResult(k,o);const{modelCursor:m,modelRange:u}=i.convertChildren(r,o.modelCursor);o.modelCursor=m,o.modelRange=a.createRange(o.modelRange.start,u.end)}}(e))))}_createBookmarkUIElement(t){return t.createUIElement("span",{class:"ck-bookmark__icon"},(function(t){const o=this.toDomElement(t),i=new r.IconView;return i.set({content:e.icons.bookmarkInline,isColorInherited:!1}),i.render(),o.appendChild(i.element),o}))}_trackBookmarkElements(){this._bookmarkElements.forEach(((e,t)=>{"$graveyard"===t.root.rootName&&this._bookmarkElements.delete(t)}))}}function S(e,t=!0){if(!("a"===e.name))return null;if(t&&!e.isEmpty)return null;const o=e.hasAttribute("id"),i=e.hasAttribute("name"),r=e.hasAttribute("href");return o&&!r?{name:!0,attributes:["id"]}:i&&!r?{name:!0,attributes:["name"]}:null}const U="bookmark-ui";class O extends e.Plugin{constructor(){super(...arguments),this.actionsView=null,this.formView=null}static get requires(){return[F,r.ContextualBalloon]}static get pluginName(){return"BookmarkUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor;e.editing.view.addObserver(s.ClickObserver),this._balloon=e.plugins.get(r.ContextualBalloon),this._createToolbarBookmarkButton(),this._enableBalloonActivators(),e.conversion.for("editingDowncast").markerToHighlight({model:U,view:{classes:["ck-fake-bookmark-selection"]}}),e.conversion.for("editingDowncast").markerToElement({model:U,view:(e,{writer:t})=>{if(!e.markerRange.isCollapsed)return null;const o=t.createUIElement("span");return t.addClass(["ck-fake-bookmark-selection","ck-fake-bookmark-selection_collapsed"],o),o}})}destroy(){super.destroy(),this.formView&&this.formView.destroy(),this.actionsView&&this.actionsView.destroy()}_createViews(){this.actionsView=this._createActionsView(),this.formView=this._createFormView(),this._enableUserBalloonInteractions()}_createActionsView(){const e=this.editor,t=new B(e.locale),o=e.commands.get("updateBookmark"),i=e.commands.get("delete");return t.bind("id").to(o,"value"),t.editButtonView.bind("isEnabled").to(o),t.removeButtonView.bind("isEnabled").to(i),this.listenTo(t,"edit",(()=>{this._addFormView()})),this.listenTo(t,"remove",(()=>{this._hideUI(),e.execute("delete")})),t.keystrokes.set("Esc",((e,t)=>{this._hideUI(),t()})),t}_createFormView(){const e=this.editor,t=e.locale,o=e.commands.get("insertBookmark"),i=e.commands.get("updateBookmark"),s=[o,i],n=new((0,r.CssTransitionDisablerMixin)(_))(t,function(e){const{t}=e,o=e.plugins.get(F);return[e=>{if(!e.id)return t("Bookmark must not be empty.")},e=>{if(e.id&&/\s/.test(e.id))return t("Bookmark name cannot contain space characters.")},i=>{const r=e.model.document.selection.getSelectedElement(),s=o.getElementForBookmarkId(i.id);if(r!==s)return s?t("Bookmark name already exists."):void 0}]}(e));return n.idInputView.fieldView.bind("value").to(i,"value"),n.idInputView.bind("isEnabled").toMany(s,"isEnabled",((...e)=>e.some((e=>e)))),n.buttonView.bind("isEnabled").toMany(s,"isEnabled",((...e)=>e.some((e=>e)))),this.listenTo(n,"submit",(()=>{if(n.isValid()){const t=n.id;this._getSelectedBookmarkElement()?e.execute("updateBookmark",{bookmarkId:t}):e.execute("insertBookmark",{bookmarkId:t}),this._closeFormView()}})),this.listenTo(n.idInputView,"change:errorText",(()=>{e.ui.update()})),n.keystrokes.set("Esc",((e,t)=>{this._closeFormView(),t()})),n}_createToolbarBookmarkButton(){const e=this.editor;e.ui.componentFactory.add("bookmark",(()=>{const e=this._createButton(r.ButtonView);return e.set({tooltip:!0}),e})),e.ui.componentFactory.add("menuBar:bookmark",(()=>this._createButton(r.MenuBarMenuListItemButtonView)))}_createButton(t){const o=this.editor,i=o.locale,r=new t(i),s=o.commands.get("insertBookmark"),n=o.commands.get("updateBookmark"),a=i.t;return r.set({label:a("Bookmark"),icon:e.icons.bookmark}),this.listenTo(r,"execute",(()=>this._showUI(!0))),r.bind("isEnabled").toMany([s,n],"isEnabled",((...e)=>e.some((e=>e)))),r.bind("isOn").to(n,"value",(e=>!!e)),r}_enableBalloonActivators(){const e=this.editor.editing.view.document;this.listenTo(e,"click",(()=>{this._getSelectedBookmarkElement()&&this._showUI()}))}_enableUserBalloonInteractions(){this.editor.keystrokes.set("Tab",((e,t)=>{this._areActionsVisible&&!this.actionsView.focusTracker.isFocused&&(this.actionsView.focus(),t())}),{priority:"high"}),this.editor.keystrokes.set("Esc",((e,t)=>{this._isUIVisible&&(this._hideUI(),t())})),(0,r.clickOutsideHandler)({emitter:this.formView,activator:()=>this._isUIInPanel,contextElements:()=>[this._balloon.view.element],callback:()=>this._hideUI()})}_updateFormButtonLabel(e){const t=this.editor.locale.t;this.formView.buttonView.label=t(e?"Update":"Insert")}_addActionsView(){this.actionsView||this._createViews(),this._areActionsInPanel||this._balloon.add({view:this.actionsView,position:this._getBalloonPositionData()})}_addFormView(){if(this.formView||this._createViews(),this._isFormInPanel)return;const e=this.editor.commands.get("updateBookmark");this.formView.disableCssTransitions(),this.formView.resetFormStatus(),this._balloon.add({view:this.formView,position:this._getBalloonPositionData()}),this.formView.idInputView.fieldView.value=e.value||"",this._balloon.visibleView===this.formView&&this.formView.idInputView.fieldView.select(),this.formView.enableCssTransitions()}_closeFormView(){void 0!==this.editor.commands.get("updateBookmark").value?this._removeFormView():this._hideUI()}_removeFormView(){this._isFormInPanel&&(this.formView.buttonView.focus(),this.formView.idInputView.fieldView.reset(),this._balloon.remove(this.formView),this.editor.editing.view.focus(),this._hideFakeVisualSelection())}_showUI(e=!1){this.formView||this._createViews(),this._getSelectedBookmarkElement()?(this._areActionsVisible?this._addFormView():this._addActionsView(),e&&this._balloon.showStack("main")):(this._showFakeVisualSelection(),this._addActionsView(),e&&this._balloon.showStack("main"),this._addFormView()),this._startUpdatingUI()}_hideUI(){if(!this._isUIInPanel)return;const e=this.editor;this.stopListening(e.ui,"update"),this.stopListening(this._balloon,"change:visibleView"),e.editing.view.focus(),this._removeFormView(),this._balloon.remove(this.actionsView),this._hideFakeVisualSelection()}_startUpdatingUI(){const e=this.editor,t=e.editing.view.document;let o=this._getSelectedBookmarkElement(),i=s();this._updateFormButtonLabel(!!o);const r=()=>{const e=this._getSelectedBookmarkElement(),t=s();o&&!e||!o&&t!==i?this._hideUI():this._isUIVisible&&this._balloon.updatePosition(this._getBalloonPositionData()),this._updateFormButtonLabel(!!o),o=e,i=t};function s(){return t.selection.focus.getAncestors().reverse().find((e=>e.is("element")))}this.listenTo(e.ui,"update",r),this.listenTo(this._balloon,"change:visibleView",r)}get _isFormInPanel(){return!!this.formView&&this._balloon.hasView(this.formView)}get _areActionsInPanel(){return!!this.actionsView&&this._balloon.hasView(this.actionsView)}get _areActionsVisible(){return!!this.actionsView&&this._balloon.visibleView===this.actionsView}get _isUIInPanel(){return this._isFormInPanel||this._areActionsInPanel}get _isUIVisible(){const e=this._balloon.visibleView;return!!this.formView&&e==this.formView||this._areActionsVisible}_getBalloonPositionData(){const e=this.editor.editing.view,t=this.editor.model;let o;const i=this._getSelectedBookmarkElement();if(t.markers.has(U)){const t=Array.from(this.editor.editing.mapper.markerNameToElements(U)),i=e.createRange(e.createPositionBefore(t[0]),e.createPositionAfter(t[t.length-1]));o=e.domConverter.viewRangeToDom(i)}else i&&(o=()=>{const t=this.editor.editing.mapper,o=e.domConverter,r=t.toViewElement(i);return o.mapViewToDom(r)});return o&&{target:o}}_getSelectedBookmarkElement(){const e=this.editor.model.document.selection.getSelectedElement();return e&&e.is("element","bookmark")?e:null}_showFakeVisualSelection(){const e=this.editor.model;e.change((t=>{const o=e.document.selection.getFirstRange();if(e.markers.has(U))t.updateMarker(U,{range:o});else if(o.start.isAtEnd){const i=o.start.getLastMatchingPosition((({item:t})=>!e.schema.isContent(t)),{boundaries:o});t.addMarker(U,{usingOperation:!1,affectsData:!1,range:t.createRange(i,o.end)})}else t.addMarker(U,{usingOperation:!1,affectsData:!1,range:o})}))}_hideFakeVisualSelection(){const e=this.editor.model;e.markers.has(U)&&e.change((e=>{e.removeMarker(U)}))}}class M extends e.Plugin{static get pluginName(){return"Bookmark"}static get requires(){return[F,O,t.Widget]}static get isOfficialPlugin(){return!0}}})(),(window.CKEditor5=window.CKEditor5||{}).bookmark=i})();
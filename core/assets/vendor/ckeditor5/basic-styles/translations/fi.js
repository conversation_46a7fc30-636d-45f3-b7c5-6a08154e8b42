!function(i){const t=i.fi=i.fi||{};t.dictionary=Object.assign(t.dictionary||{},{Bold:"Lihavointi","Bold text":"Lihavoitu teksti",Code:"<PERSON>od<PERSON>",Italic:"Kursivointi","Italic text":"Kursivoitu teksti","Move out of an inline code style":"<PERSON><PERSON>ry pois rivinsisäisestä koodista",Strikethrough:"Yliviivaus","Strikethrough text":"Yli<PERSON>ivattu teksti",Subscript:"<PERSON><PERSON><PERSON>",Superscript:"<PERSON><PERSON><PERSON>indek<PERSON>",Underline:"Alleviivaus","Underline text":"Alleviivattu teksti"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));
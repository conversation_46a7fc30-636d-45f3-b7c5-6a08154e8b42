!function(n){const i=n.vi=n.vi||{};i.dictionary=Object.assign(i.dictionary||{},{Bold:"Đậm","Bold text":"In đậm chữ",Code:"Code",Italic:"<PERSON><PERSON>êng","Italic text":"In nghiêng chữ","Move out of an inline code style":"Thoát khỏi kiểu mã nội dòng",Strikethrough:"Gạch ngang","Strikethrough text":"Gạch ngang chữ",Subscript:"Chữ nhỏ dưới",Superscript:"Chữ nhỏ trên",Underline:"Gạch dưới","Underline text":"Gạch chân chữ"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));
!function(t){const e=t.en=t.en||{};e.dictionary=Object.assign(e.dictionary||{},{Bold:"Bold","Bold text":"Bold text",Code:"Code",Italic:"Italic","Italic text":"Italic text","Move out of an inline code style":"Move out of an inline code style",Strikethrough:"Strikethrough","Strikethrough text":"Strikethrough text",Subscript:"Subscript",Superscript:"Superscript",Underline:"Underline","Underline text":"Underline text"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={598:(t,e,i)=>{"use strict";i.d(e,{A:()=>a});var n=i(758),r=i.n(n),s=i(935),o=i.n(s)()(r());o.push([t.id,".ck-content code{background-color:hsla(0,0%,78%,.3);border-radius:2px;padding:.15em}.ck.ck-editor__editable .ck-code_selected{background-color:hsla(0,0%,78%,.5)}",""]);const a=o},935:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var i="",n=void 0!==e[5];return e[4]&&(i+="@supports (".concat(e[4],") {")),e[2]&&(i+="@media ".concat(e[2]," {")),n&&(i+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),i+=t(e),n&&(i+="}"),e[2]&&(i+="}"),e[4]&&(i+="}"),i})).join("")},e.i=function(t,i,n,r,s){"string"==typeof t&&(t=[[null,t,void 0]]);var o={};if(n)for(var a=0;a<this.length;a++){var c=this[a][0];null!=c&&(o[c]=!0)}for(var l=0;l<t.length;l++){var u=[].concat(t[l]);n&&o[u[0]]||(void 0!==s&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=s),i&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=i):u[2]=i),r&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=r):u[4]="".concat(r)),e.push(u))}},e}},758:t=>{"use strict";t.exports=function(t){return t[1]}},591:t=>{"use strict";var e=[];function i(t){for(var i=-1,n=0;n<e.length;n++)if(e[n].identifier===t){i=n;break}return i}function n(t,n){for(var s={},o=[],a=0;a<t.length;a++){var c=t[a],l=n.base?c[0]+n.base:c[0],u=s[l]||0,d="".concat(l," ").concat(u);s[l]=u+1;var g=i(d),m={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==g)e[g].references++,e[g].updater(m);else{var p=r(m,n);n.byIndex=a,e.splice(a,0,{identifier:d,updater:p,references:1})}o.push(d)}return o}function r(t,e){var i=e.domAPI(e);i.update(t);return function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;i.update(t=e)}else i.remove()}}t.exports=function(t,r){var s=n(t=t||[],r=r||{});return function(t){t=t||[];for(var o=0;o<s.length;o++){var a=i(s[o]);e[a].references--}for(var c=n(t,r),l=0;l<s.length;l++){var u=i(s[l]);0===e[u].references&&(e[u].updater(),e.splice(u,1))}s=c}}},128:t=>{"use strict";var e={};t.exports=function(t,i){var n=function(t){if(void 0===e[t]){var i=document.querySelector(t);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(t){i=null}e[t]=i}return e[t]}(t);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(i)}},51:t=>{"use strict";t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},21:t=>{"use strict";t.exports=function(t,e){Object.keys(e).forEach((function(i){t.setAttribute(i,e[i])}))}},639:t=>{"use strict";var e,i=(e=[],function(t,i){return e[t]=i,e.filter(Boolean).join("\n")});function n(t,e,n,r){var s;if(n)s="";else{s="",r.supports&&(s+="@supports (".concat(r.supports,") {")),r.media&&(s+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(s+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),s+=r.css,o&&(s+="}"),r.media&&(s+="}"),r.supports&&(s+="}")}if(t.styleSheet)t.styleSheet.cssText=i(e,s);else{var a=document.createTextNode(s),c=t.childNodes;c[e]&&t.removeChild(c[e]),c.length?t.insertBefore(a,c[e]):t.appendChild(a)}}var r={singleton:null,singletonCounter:0};t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=r.singletonCounter++,i=r.singleton||(r.singleton=t.insertStyleElement(t));return{update:function(t){n(i,e,!1,t)},remove:function(t){n(i,e,!0,t)}}}},782:(t,e,i)=>{t.exports=i(237)("./src/core.js")},834:(t,e,i)=>{t.exports=i(237)("./src/typing.js")},311:(t,e,i)=>{t.exports=i(237)("./src/ui.js")},237:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function i(n){var r=e[n];if(void 0!==r)return r.exports;var s=e[n]={id:n,exports:{}};return t[n](s,s.exports,i),s.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};(()=>{"use strict";i.r(n),i.d(n,{AttributeCommand:()=>e,Bold:()=>u,BoldEditing:()=>s,BoldUI:()=>l,Code:()=>E,CodeEditing:()=>m,CodeUI:()=>A,Italic:()=>M,ItalicEditing:()=>T,ItalicUI:()=>N,Strikethrough:()=>K,StrikethroughEditing:()=>F,StrikethroughUI:()=>V,Subscript:()=>q,SubscriptEditing:()=>z,SubscriptUI:()=>j,Superscript:()=>G,SuperscriptEditing:()=>$,SuperscriptUI:()=>X,Underline:()=>Z,UnderlineEditing:()=>Q,UnderlineUI:()=>Y});var t=i(782);class e extends t.Command{constructor(t,e){super(t),this.attributeKey=e}refresh(){const t=this.editor.model,e=t.document;this.value=this._getValueFromFirstAllowedNode(),this.isEnabled=t.schema.checkAttributeInSelection(e.selection,this.attributeKey)}execute(t={}){const e=this.editor.model,i=e.document.selection,n=void 0===t.forceValue?!this.value:t.forceValue;e.change((t=>{if(i.isCollapsed)n?t.setSelectionAttribute(this.attributeKey,!0):t.removeSelectionAttribute(this.attributeKey);else{const r=e.schema.getValidRanges(i.getRanges(),this.attributeKey);for(const e of r)n?t.setAttribute(this.attributeKey,n,e):t.removeAttribute(this.attributeKey,e)}}))}_getValueFromFirstAllowedNode(){const t=this.editor.model,e=t.schema,i=t.document.selection;if(i.isCollapsed)return i.hasAttribute(this.attributeKey);for(const t of i.getRanges())for(const i of t.getItems())if(e.checkAttribute(i,this.attributeKey))return i.hasAttribute(this.attributeKey);return!1}}const r="bold";class s extends t.Plugin{static get pluginName(){return"BoldEditing"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,i=this.editor.t;t.model.schema.extend("$text",{allowAttributes:r}),t.model.schema.setAttributeProperties(r,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:r,view:"strong",upcastAlso:["b",t=>{const e=t.getStyle("font-weight");return e&&("bold"==e||Number(e)>=600)?{name:!0,styles:["font-weight"]}:null}]}),t.commands.add(r,new e(t,r)),t.keystrokes.set("CTRL+B",r),t.accessibility.addKeystrokeInfos({keystrokes:[{label:i("Bold text"),keystroke:"CTRL+B"}]})}}var o=i(311);function a({editor:t,commandName:e,plugin:i,icon:n,label:r,keystroke:s}){return a=>{const c=t.commands.get(e),l=new a(t.locale);return l.set({label:r,icon:n,keystroke:s,isToggleable:!0}),l.bind("isEnabled").to(c,"isEnabled"),l.bind("isOn").to(c,"value"),l instanceof o.MenuBarMenuListItemButtonView?l.set({role:"menuitemcheckbox"}):l.set({tooltip:!0}),i.listenTo(l,"execute",(()=>{t.execute(e),t.editing.view.focus()})),l}}const c="bold";class l extends t.Plugin{static get pluginName(){return"BoldUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor,i=e.locale.t,n=a({editor:e,commandName:c,plugin:this,icon:t.icons.bold,label:i("Bold"),keystroke:"CTRL+B"});e.ui.componentFactory.add(c,(()=>n(o.ButtonView))),e.ui.componentFactory.add("menuBar:"+c,(()=>n(o.MenuBarMenuListItemButtonView)))}}class u extends t.Plugin{static get requires(){return[s,l]}static get pluginName(){return"Bold"}static get isOfficialPlugin(){return!0}}var d=i(834);const g="code";class m extends t.Plugin{static get pluginName(){return"CodeEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[d.TwoStepCaretMovement]}init(){const t=this.editor,i=this.editor.t;t.model.schema.extend("$text",{allowAttributes:g}),t.model.schema.setAttributeProperties(g,{isFormatting:!0,copyOnEnter:!1}),t.conversion.attributeToElement({model:g,view:"code",upcastAlso:{styles:{"word-wrap":"break-word"}}}),t.commands.add(g,new e(t,g)),t.plugins.get(d.TwoStepCaretMovement).registerAttribute(g),(0,d.inlineHighlight)(t,g,"code","ck-code_selected"),t.accessibility.addKeystrokeInfos({keystrokes:[{label:i("Move out of an inline code style"),keystroke:[["arrowleft","arrowleft"],["arrowright","arrowright"]]}]})}}var p=i(591),h=i.n(p),f=i(639),b=i.n(f),v=i(128),y=i.n(v),w=i(21),x=i.n(w),k=i(51),I=i.n(k),P=i(598),S={attributes:{"data-cke":!0}};S.setAttributes=x(),S.insert=y().bind(null,"head"),S.domAPI=b(),S.insertStyleElement=I();h()(P.A,S);P.A&&P.A.locals&&P.A.locals;const B="code";class A extends t.Plugin{static get pluginName(){return"CodeUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,e=t.locale.t,i=a({editor:t,commandName:B,plugin:this,icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m12.5 5.7 5.2 3.9v1.3l-5.6 4c-.1.2-.3.2-.5.2-.3-.1-.6-.7-.6-1l.3-.4 4.7-3.5L11.5 7l-.2-.2c-.1-.3-.1-.6 0-.8.2-.2.5-.4.8-.4a.8.8 0 0 1 .4.1zm-5.2 0L2 9.6v1.3l5.6 4c.1.2.3.2.5.2.3-.1.7-.7.6-1 0-.1 0-.3-.2-.4l-5-3.5L8.2 7l.2-.2c.1-.3.1-.6 0-.8-.2-.2-.5-.4-.8-.4a.8.8 0 0 0-.3.1z"/></svg>',label:e("Code")});t.ui.componentFactory.add(B,(()=>i(o.ButtonView))),t.ui.componentFactory.add("menuBar:"+B,(()=>i(o.MenuBarMenuListItemButtonView)))}}class E extends t.Plugin{static get requires(){return[m,A]}static get pluginName(){return"Code"}static get isOfficialPlugin(){return!0}}const O="italic";class T extends t.Plugin{static get pluginName(){return"ItalicEditing"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,i=this.editor.t;t.model.schema.extend("$text",{allowAttributes:O}),t.model.schema.setAttributeProperties(O,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:O,view:"i",upcastAlso:["em",{styles:{"font-style":"italic"}}]}),t.commands.add(O,new e(t,O)),t.keystrokes.set("CTRL+I",O),t.accessibility.addKeystrokeInfos({keystrokes:[{label:i("Italic text"),keystroke:"CTRL+I"}]})}}const C="italic";class N extends t.Plugin{static get pluginName(){return"ItalicUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,e=t.locale.t,i=a({editor:t,commandName:C,plugin:this,icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m9.586 14.633.021.004c-.036.335.095.655.393.962.082.083.173.15.274.201h1.474a.6.6 0 1 1 0 1.2H5.304a.6.6 0 0 1 0-1.2h1.15c.474-.07.809-.182 1.005-.334.157-.122.291-.32.404-.597l2.416-9.55a1.053 1.053 0 0 0-.281-.823 1.12 1.12 0 0 0-.442-.296H8.15a.6.6 0 0 1 0-1.2h6.443a.6.6 0 1 1 0 1.2h-1.195c-.376.056-.65.155-.823.296-.215.175-.423.439-.623.79l-2.366 9.347z"/></svg>',keystroke:"CTRL+I",label:e("Italic")});t.ui.componentFactory.add(C,(()=>i(o.ButtonView))),t.ui.componentFactory.add("menuBar:"+C,(()=>i(o.MenuBarMenuListItemButtonView)))}}class M extends t.Plugin{static get requires(){return[T,N]}static get pluginName(){return"Italic"}static get isOfficialPlugin(){return!0}}const L="strikethrough";class F extends t.Plugin{static get pluginName(){return"StrikethroughEditing"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,i=this.editor.t;t.model.schema.extend("$text",{allowAttributes:L}),t.model.schema.setAttributeProperties(L,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:L,view:"s",upcastAlso:["del","strike",{styles:{"text-decoration":"line-through"}}]}),t.commands.add(L,new e(t,L)),t.keystrokes.set("CTRL+SHIFT+X","strikethrough"),t.accessibility.addKeystrokeInfos({keystrokes:[{label:i("Strikethrough text"),keystroke:"CTRL+SHIFT+X"}]})}}const U="strikethrough";class V extends t.Plugin{static get pluginName(){return"StrikethroughUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,e=t.locale.t,i=a({editor:t,commandName:U,plugin:this,icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M7 16.4c-.8-.4-1.5-.9-2.2-1.5a.6.6 0 0 1-.2-.5l.3-.6h1c1 1.2 2.1 1.7 3.7 1.7 1 0 1.8-.3 2.3-.6.6-.4.6-1.2.6-1.3.2-1.2-.9-2.1-.9-2.1h2.1c.3.7.4 1.2.4 1.7v.8l-.6 1.2c-.6.8-1.1 1-1.6 1.2a6 6 0 0 1-2.4.6c-1 0-1.8-.3-2.5-.6zM6.8 9 6 8.3c-.4-.5-.5-.8-.5-1.6 0-.7.1-1.3.5-1.8.4-.6 1-1 1.6-1.3a6.3 6.3 0 0 1 4.7 0 4 4 0 0 1 1.7 1l.3.7c0 .1.2.4-.2.7-.4.2-.9.1-1 0a3 3 0 0 0-1.2-1c-.4-.2-1-.3-2-.4-.7 0-1.4.2-2 .6-.8.6-1 .8-1 1.5 0 .8.5 1 1.2 ******* 1.1.7 1.9 1H6.8z"/><path d="M3 10.5V9h14v1.5z"/></svg>',keystroke:"CTRL+SHIFT+X",label:e("Strikethrough")});t.ui.componentFactory.add(U,(()=>i(o.ButtonView))),t.ui.componentFactory.add("menuBar:"+U,(()=>i(o.MenuBarMenuListItemButtonView)))}}class K extends t.Plugin{static get requires(){return[F,V]}static get pluginName(){return"Strikethrough"}static get isOfficialPlugin(){return!0}}const R="subscript";class z extends t.Plugin{static get pluginName(){return"SubscriptEditing"}static get isOfficialPlugin(){return!0}init(){const t=this.editor;t.model.schema.extend("$text",{allowAttributes:R}),t.model.schema.setAttributeProperties(R,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:R,view:"sub",upcastAlso:[{styles:{"vertical-align":"sub"}}]}),t.commands.add(R,new e(t,R))}}const _="subscript";class j extends t.Plugin{static get pluginName(){return"SubscriptUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,e=t.locale.t,i=a({editor:t,commandName:_,plugin:this,icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m7.03 10.349 3.818-3.819a.8.8 0 1 1 1.132 1.132L8.16 11.48l3.819 3.818a.8.8 0 1 1-1.132 1.132L7.03 12.61l-3.818 3.82a.8.8 0 1 1-1.132-1.132L5.9 11.48 2.08 7.662A.8.8 0 1 1 3.212 6.53l3.818 3.82zm8.147 7.829h2.549c.254 0 .447.05.58.152a.49.49 0 0 1 .201.413.54.54 0 0 1-.159.393c-.105.108-.266.162-.48.162h-3.594c-.245 0-.435-.066-.572-.197a.621.621 0 0 1-.205-.463c0-.114.044-.265.132-.453a1.62 1.62 0 0 1 .288-.444c.433-.436.824-.81 1.172-1.122.348-.312.597-.517.747-.615.267-.183.49-.368.667-.553.177-.185.312-.375.405-.57.093-.194.139-.384.139-.57a1.008 1.008 0 0 0-.554-.917 1.197 1.197 0 0 0-.56-.133c-.426 0-.761.182-1.005.546a2.332 2.332 0 0 0-.164.39 1.609 1.609 0 0 1-.258.488c-.096.114-.237.17-.423.17a.558.558 0 0 1-.405-.156.568.568 0 0 1-.161-.427c0-.218.05-.446.151-.683.101-.238.252-.453.452-.646s.454-.349.762-.467a2.998 2.998 0 0 1 1.081-.178c.498 0 .923.076 1.274.228a1.916 1.916 0 0 1 1.004 1.032 1.984 1.984 0 0 1-.156 1.794c-.2.32-.405.572-.613.754-.208.182-.558.468-1.048.857-.49.39-.826.691-1.008.906a2.703 2.703 0 0 0-.24.309z"/></svg>',label:e("Subscript")});t.ui.componentFactory.add(_,(()=>i(o.ButtonView))),t.ui.componentFactory.add("menuBar:"+_,(()=>i(o.MenuBarMenuListItemButtonView)))}}class q extends t.Plugin{static get requires(){return[z,j]}static get pluginName(){return"Subscript"}static get isOfficialPlugin(){return!0}}const H="superscript";class $ extends t.Plugin{static get pluginName(){return"SuperscriptEditing"}static get isOfficialPlugin(){return!0}init(){const t=this.editor;t.model.schema.extend("$text",{allowAttributes:H}),t.model.schema.setAttributeProperties(H,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:H,view:"sup",upcastAlso:[{styles:{"vertical-align":"super"}}]}),t.commands.add(H,new e(t,H))}}const D="superscript";class X extends t.Plugin{static get pluginName(){return"SuperscriptUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,e=t.locale.t,i=a({editor:t,commandName:D,plugin:this,icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M15.677 8.678h2.549c.254 0 .447.05.58.152a.49.49 0 0 1 .201.413.54.54 0 0 1-.159.393c-.105.108-.266.162-.48.162h-3.594c-.245 0-.435-.066-.572-.197a.621.621 0 0 1-.205-.463c0-.114.044-.265.132-.453a1.62 1.62 0 0 1 .288-.444c.433-.436.824-.81 1.172-1.122.348-.312.597-.517.747-.615.267-.183.49-.368.667-.553.177-.185.312-.375.405-.57.093-.194.139-.384.139-.57a1.008 1.008 0 0 0-.554-.917 1.197 1.197 0 0 0-.56-.133c-.426 0-.761.182-1.005.546a2.332 2.332 0 0 0-.164.39 1.609 1.609 0 0 1-.258.488c-.096.114-.237.17-.423.17a.558.558 0 0 1-.405-.156.568.568 0 0 1-.161-.427c0-.218.05-.446.151-.683.101-.238.252-.453.452-.646s.454-.349.762-.467a2.998 2.998 0 0 1 1.081-.178c.498 0 .923.076 1.274.228a1.916 1.916 0 0 1 1.004 1.032 1.984 1.984 0 0 1-.156 1.794c-.2.32-.405.572-.613.754-.208.182-.558.468-1.048.857-.49.39-.826.691-1.008.906a2.703 2.703 0 0 0-.24.309zM7.03 10.349l3.818-3.819a.8.8 0 1 1 1.132 1.132L8.16 11.48l3.819 3.818a.8.8 0 1 1-1.132 1.132L7.03 12.61l-3.818 3.82a.8.8 0 1 1-1.132-1.132L5.9 11.48 2.08 7.662A.8.8 0 1 1 3.212 6.53l3.818 3.82z"/></svg>',label:e("Superscript")});t.ui.componentFactory.add(D,(()=>i(o.ButtonView))),t.ui.componentFactory.add("menuBar:"+D,(()=>i(o.MenuBarMenuListItemButtonView)))}}class G extends t.Plugin{static get requires(){return[$,X]}static get pluginName(){return"Superscript"}static get isOfficialPlugin(){return!0}}const J="underline";class Q extends t.Plugin{static get pluginName(){return"UnderlineEditing"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,i=this.editor.t;t.model.schema.extend("$text",{allowAttributes:J}),t.model.schema.setAttributeProperties(J,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:J,view:"u",upcastAlso:{styles:{"text-decoration":"underline"}}}),t.commands.add(J,new e(t,J)),t.keystrokes.set("CTRL+U","underline"),t.accessibility.addKeystrokeInfos({keystrokes:[{label:i("Underline text"),keystroke:"CTRL+U"}]})}}const W="underline";class Y extends t.Plugin{static get pluginName(){return"UnderlineUI"}static get isOfficialPlugin(){return!0}init(){const t=this.editor,e=t.locale.t,i=a({editor:t,commandName:W,plugin:this,icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M3 18v-1.5h14V18zm2.2-8V3.6c0-.4.4-.6.8-.6.3 0 .7.2.7.6v6.2c0 2 1.3 2.8 3.2 2.8 1.9 0 3.4-.9 3.4-2.9V3.6c0-.3.4-.5.8-.5.3 0 .7.2.7.5V10c0 2.7-2.2 4-4.9 4-2.6 0-4.7-1.2-4.7-4z"/></svg>',label:e("Underline"),keystroke:"CTRL+U"});t.ui.componentFactory.add(W,(()=>i(o.ButtonView))),t.ui.componentFactory.add("menuBar:"+W,(()=>i(o.MenuBarMenuListItemButtonView)))}}class Z extends t.Plugin{static get requires(){return[Q,Y]}static get pluginName(){return"Underline"}static get isOfficialPlugin(){return!0}}})(),(window.CKEditor5=window.CKEditor5||{}).basicStyles=n})();
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={782:(t,e,o)=>{t.exports=o(237)("./src/core.js")},783:(t,e,o)=>{t.exports=o(237)("./src/engine.js")},311:(t,e,o)=>{t.exports=o(237)("./src/ui.js")},584:(t,e,o)=>{t.exports=o(237)("./src/utils.js")},237:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function o(r){var i=e[r];if(void 0!==i)return i.exports;var n=e[r]={exports:{}};return t[r](n,n.exports,o),n.exports}o.d=(t,e)=>{for(var r in e)o.o(e,r)&&!o.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};(()=>{"use strict";o.r(r),o.d(r,{DecoupledEditor:()=>F});var t=o(782),e=o(584),i=o(311),n=o(783);class s extends i.EditorUI{constructor(t,e){super(t),this.view=e}init(){const t=this.editor,e=this.view,o=t.editing.view,r=e.editable,i=o.document.getRoot();r.name=i.rootName,e.render();const n=r.element;this.setEditableElement(r.name,n),e.editable.bind("isFocused").to(this.focusTracker),o.attachDomRoot(n),this._initPlaceholder(),this._initToolbar(),this._initMenuBar(this.view.menuBarView),this.fire("ready")}destroy(){super.destroy();const t=this.view,e=this.editor.editing.view;e.getDomRoot(t.editable.name)&&e.detachDomRoot(t.editable.name),t.destroy()}_initToolbar(){const t=this.editor,e=this.view;e.toolbar.fillFromConfig(t.config.get("toolbar"),this.componentFactory),this.addToolbar(e.toolbar)}_initPlaceholder(){const t=this.editor,e=t.editing.view,o=e.document.getRoot(),r=t.config.get("placeholder");if(r){const t="string"==typeof r?r:r[o.rootName];t&&(o.placeholder=t)}(0,n.enablePlaceholder)({view:e,element:o,isDirectHost:!1,keepOnFocus:!0})}}class l extends i.EditorUIView{constructor(t,e,o={}){super(t),this.toolbar=new i.ToolbarView(t,{shouldGroupWhenFull:o.shouldToolbarGroupWhenFull}),this.menuBarView=new i.MenuBarView(t),this.editable=new i.InlineEditableUIView(t,e,o.editableElement,{label:o.label}),this.toolbar.extendTemplate({attributes:{class:["ck-reset_all","ck-rounded-corners"],dir:t.uiLanguageDirection}}),this.menuBarView.extendTemplate({attributes:{class:["ck-reset_all","ck-rounded-corners"],dir:t.uiLanguageDirection}})}render(){super.render(),this.registerChild([this.menuBarView,this.toolbar,this.editable])}}const a=function(t){return null!=t&&"object"==typeof t};const c="object"==typeof global&&global&&global.Object===Object&&global;var u="object"==typeof self&&self&&self.Object===Object&&self;const d=(c||u||Function("return this")()).Symbol;var h=Object.prototype,b=h.hasOwnProperty,p=h.toString,g=d?d.toStringTag:void 0;const f=function(t){var e=b.call(t,g),o=t[g];try{t[g]=void 0;var r=!0}catch(t){}var i=p.call(t);return r&&(e?t[g]=o:delete t[g]),i};var m=Object.prototype.toString;const w=function(t){return m.call(t)};var v=d?d.toStringTag:void 0;const y=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":v&&v in Object(t)?f(t):w(t)};const j=function(t,e){return function(o){return t(e(o))}}(Object.getPrototypeOf,Object);var E=Function.prototype,O=Object.prototype,T=E.toString,x=O.hasOwnProperty,D=T.call(Object);const S=function(t){if(!a(t)||"[object Object]"!=y(t))return!1;var e=j(t);if(null===e)return!0;var o=x.call(e,"constructor")&&e.constructor;return"function"==typeof o&&o instanceof o&&T.call(o)==D};const P=function(t){return a(t)&&1===t.nodeType&&!S(t)};class F extends((0,t.ElementApiMixin)(t.Editor)){static get editorName(){return"DecoupledEditor"}constructor(o,r={}){if(!_(o)&&void 0!==r.initialData)throw new e.CKEditorError("editor-create-initial-data",null);super(r),void 0===this.config.get("initialData")&&this.config.set("initialData",function(t){return _(t)?(0,e.getDataFromElement)(t):t}(o)),_(o)&&(this.sourceElement=o,(0,t.secureSourceElement)(this,o)),this.model.document.createRoot();const i=!this.config.get("toolbar.shouldNotGroupWhenFull"),n=new l(this.locale,this.editing.view,{editableElement:this.sourceElement,shouldToolbarGroupWhenFull:i,label:this.config.get("label")});this.ui=new s(this,n)}destroy(){const t=this.getData();return this.ui.destroy(),super.destroy().then((()=>{this.sourceElement&&this.updateSourceElement(t)}))}static create(t,o={}){return new Promise((r=>{if(_(t)&&"TEXTAREA"===t.tagName)throw new e.CKEditorError("editor-wrong-element",null);const i=new this(t,o);r(i.initPlugins().then((()=>i.ui.init())).then((()=>i.data.init(i.config.get("initialData")))).then((()=>i.fire("ready"))).then((()=>i)))}))}}function _(t){return P(t)}})(),(window.CKEditor5=window.CKEditor5||{}).editorDecoupled=r})();
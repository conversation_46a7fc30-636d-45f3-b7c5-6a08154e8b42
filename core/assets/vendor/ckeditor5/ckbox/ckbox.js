!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Cannot access default workspace.":"Cannot access default workspace.","Cannot determine a category for the uploaded file.":"Cannot determine a category for the uploaded file.","Edit image":"Edit image","Failed to determine category of edited image.":"Failed to determine category of edited image.","Open file manager":"Open file manager","Processing the edited image.":"Processing the edited image.","Server failed to process the image.":"Server failed to process the image.","You have no image editing permissions.":"You have no image editing permissions."})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={957:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(758),o=r.n(n),i=r(935),s=r.n(i)()(o());s.push([e.id,':root{--ck-image-processing-highlight-color:#f9fafa;--ck-image-processing-background-color:#e3e5e8}.ck.ck-editor__editable .image.image-processing{position:relative}.ck.ck-editor__editable .image.image-processing:before{animation:ck-image-processing-animation 2s linear infinite;background:linear-gradient(90deg,var(--ck-image-processing-background-color),var(--ck-image-processing-highlight-color),var(--ck-image-processing-background-color));background-size:200% 100%;content:"";height:100%;left:0;position:absolute;top:0;width:100%;z-index:1}.ck.ck-editor__editable .image.image-processing img{height:100%}@keyframes ck-image-processing-animation{0%{background-position:200% 0}to{background-position:-200% 0}}',""]);const a=s},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r="",n=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),n&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),n&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r})).join("")},t.i=function(e,r,n,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var s={};if(n)for(var a=0;a<this.length;a++){var c=this[a][0];null!=c&&(s[c]=!0)}for(var l=0;l<e.length;l++){var u=[].concat(e[l]);n&&s[u[0]]||(void 0!==i&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=i),r&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=r):u[2]=r),o&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=o):u[4]="".concat(o)),t.push(u))}},t}},758:e=>{"use strict";e.exports=function(e){return e[1]}},591:e=>{"use strict";var t=[];function r(e){for(var r=-1,n=0;n<t.length;n++)if(t[n].identifier===e){r=n;break}return r}function n(e,n){for(var i={},s=[],a=0;a<e.length;a++){var c=e[a],l=n.base?c[0]+n.base:c[0],u=i[l]||0,d="".concat(l," ").concat(u);i[l]=u+1;var g=r(d),h={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==g)t[g].references++,t[g].updater(h);else{var p=o(h,n);n.byIndex=a,t.splice(a,0,{identifier:d,updater:p,references:1})}s.push(d)}return s}function o(e,t){var r=t.domAPI(t);r.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;r.update(e=t)}else r.remove()}}e.exports=function(e,o){var i=n(e=e||[],o=o||{});return function(e){e=e||[];for(var s=0;s<i.length;s++){var a=r(i[s]);t[a].references--}for(var c=n(e,o),l=0;l<i.length;l++){var u=r(i[l]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}i=c}}},128:e=>{"use strict";var t={};e.exports=function(e,r){var n=function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[e]=r}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(r){e.setAttribute(r,t[r])}))}},639:e=>{"use strict";var t,r=(t=[],function(e,r){return t[e]=r,t.filter(Boolean).join("\n")});function n(e,t,n,o){var i;if(n)i="";else{i="",o.supports&&(i+="@supports (".concat(o.supports,") {")),o.media&&(i+="@media ".concat(o.media," {"));var s=void 0!==o.layer;s&&(i+="@layer".concat(o.layer.length>0?" ".concat(o.layer):""," {")),i+=o.css,s&&(i+="}"),o.media&&(i+="}"),o.supports&&(i+="}")}if(e.styleSheet)e.styleSheet.cssText=r(t,i);else{var a=document.createTextNode(i),c=e.childNodes;c[t]&&e.removeChild(c[t]),c.length?e.insertBefore(a,c[t]):e.appendChild(a)}}var o={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=o.singletonCounter++,r=o.singleton||(o.singleton=e.insertStyleElement(e));return{update:function(e){n(r,t,!1,e)},remove:function(e){n(r,t,!0,e)}}}},782:(e,t,r)=>{e.exports=r(237)("./src/core.js")},783:(e,t,r)=>{e.exports=r(237)("./src/engine.js")},311:(e,t,r)=>{e.exports=r(237)("./src/ui.js")},260:(e,t,r)=>{e.exports=r(237)("./src/upload.js")},584:(e,t,r)=>{e.exports=r(237)("./src/utils.js")},237:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={id:n,exports:{}};return e[n](i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";r.r(n),r.d(n,{CKBox:()=>U,CKBoxEditing:()=>O,CKBoxImageEdit:()=>Tr,CKBoxImageEditEditing:()=>kr,CKBoxImageEditUI:()=>xr,CKBoxUI:()=>o});var e=r(782),t=r(311);class o extends e.Plugin{static get pluginName(){return"CKBoxUI"}static get isOfficialPlugin(){return!0}afterInit(){const e=this.editor;e.commands.get("ckbox")&&(e.ui.componentFactory.add("ckbox",(()=>this._createFileToolbarButton())),e.ui.componentFactory.add("menuBar:ckbox",(()=>this._createFileMenuBarButton())),e.plugins.has("ImageInsertUI")&&e.plugins.get("ImageInsertUI").registerIntegration({name:"assetManager",observable:()=>e.commands.get("ckbox"),buttonViewCreator:()=>this._createImageToolbarButton(),formViewCreator:()=>this._createImageDropdownButton(),menuBarButtonViewCreator:e=>this._createImageMenuBarButton(e?"insertOnly":"insertNested")}))}_createButton(e){const t=this.editor,r=new e(t.locale),n=t.commands.get("ckbox");return r.bind("isOn","isEnabled").to(n,"value","isEnabled"),r.on("execute",(()=>{t.execute("ckbox")})),r}_createFileToolbarButton(){const r=this.editor.locale.t,n=this._createButton(t.ButtonView);return n.icon=e.icons.browseFiles,n.label=r("Open file manager"),n.tooltip=!0,n}_createImageToolbarButton(){const r=this.editor.locale.t,n=this.editor.plugins.get("ImageInsertUI"),o=this._createButton(t.ButtonView);return o.icon=e.icons.imageAssetManager,o.bind("label").to(n,"isImageSelected",(e=>r(e?"Replace image with file manager":"Insert image with file manager"))),o.tooltip=!0,o}_createImageDropdownButton(){const r=this.editor.locale.t,n=this.editor.plugins.get("ImageInsertUI"),o=this._createButton(t.ButtonView);return o.icon=e.icons.imageAssetManager,o.withText=!0,o.bind("label").to(n,"isImageSelected",(e=>r(e?"Replace with file manager":"Insert with file manager"))),o.on("execute",(()=>{n.dropdownView.isOpen=!1})),o}_createFileMenuBarButton(){const r=this.editor.locale.t,n=this._createButton(t.MenuBarMenuListItemButtonView);return n.icon=e.icons.browseFiles,n.withText=!0,n.label=r("File"),n}_createImageMenuBarButton(r){const n=this.editor.locale.t,o=this.editor.locale.t,i=this._createButton(t.MenuBarMenuListItemButtonView);switch(i.icon=e.icons.imageAssetManager,i.withText=!0,r){case"insertOnly":i.label=n("Image");break;case"insertNested":i.label=o("With file manager")}return i}}var i=r(783),s=r(584),a=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","#","$","%","*","+",",","-",".",":",";","=","?","@","[","]","^","_","{","|","}","~"],c=e=>{let t=0;for(let r=0;r<e.length;r++){let n=e[r];t=83*t+a.indexOf(n)}return t},l=e=>{let t=e/255;return t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)},u=e=>{let t=Math.max(0,Math.min(1,e));return t<=.0031308?Math.trunc(12.92*t*255+.5):Math.trunc(255*(1.055*Math.pow(t,.4166666666666667)-.055)+.5)},d=(e,t)=>(e=>e<0?-1:1)(e)*Math.pow(Math.abs(e),t),g=class extends Error{constructor(e){super(e),this.name="ValidationError",this.message=e}},h=e=>{if(!e||e.length<6)throw new g("The blurhash string must be at least 6 characters");let t=c(e[0]),r=Math.floor(t/9)+1,n=t%9+1;if(e.length!==4+2*n*r)throw new g(`blurhash length mismatch: length is ${e.length} but it should be ${4+2*n*r}`)},p=e=>{let t=e>>8&255,r=255&e;return[l(e>>16),l(t),l(r)]},m=(e,t)=>{let r=Math.floor(e/361),n=Math.floor(e/19)%19,o=e%19;return[d((r-9)/9,2)*t,d((n-9)/9,2)*t,d((o-9)/9,2)*t]},f=(e,t,r,n)=>{h(e),n|=1;let o=c(e[0]),i=Math.floor(o/9)+1,s=o%9+1,a=(c(e[1])+1)/166,l=new Array(s*i);for(let t=0;t<l.length;t++)if(0===t){let r=c(e.substring(2,6));l[t]=p(r)}else{let r=c(e.substring(4+2*t,6+2*t));l[t]=m(r,a*n)}let d=4*t,g=new Uint8ClampedArray(d*r);for(let e=0;e<r;e++)for(let n=0;n<t;n++){let o=0,a=0,c=0;for(let u=0;u<i;u++)for(let i=0;i<s;i++){let d=Math.cos(Math.PI*n*i/t)*Math.cos(Math.PI*e*u/r),g=l[i+u*s];o+=g[0]*d,a+=g[1]*d,c+=g[2]*d}let h=u(o),p=u(a),m=u(c);g[4*n+0+e*d]=h,g[4*n+1+e*d]=p,g[4*n+2+e*d]=m,g[4*n+3+e*d]=255}return g};function b(e){const t=[];let r=0;for(const n in e){const o=parseInt(n,10);isNaN(o)||(o>r&&(r=o),t.push(`${e[n]} ${n}w`))}const n=[{srcset:t.join(","),sizes:`(max-width: ${r}px) 100vw, ${r}px`,type:"image/webp"}];return{imageFallbackUrl:e.default,imageSources:n}}function v({url:e,method:t="GET",data:r,onUploadProgress:n,signal:o,authorization:i}){const s=new XMLHttpRequest;s.open(t,e.toString()),s.setRequestHeader("Authorization",i),s.setRequestHeader("CKBox-Version","CKEditor 5"),s.responseType="json";const a=()=>{s.abort()};return new Promise(((e,t)=>{o.throwIfAborted(),o.addEventListener("abort",a),s.addEventListener("loadstart",(()=>{o.addEventListener("abort",a)})),s.addEventListener("loadend",(()=>{o.removeEventListener("abort",a)})),s.addEventListener("error",(()=>{t()})),s.addEventListener("abort",(()=>{t()})),s.addEventListener("load",(()=>{const r=s.response;if(!r||r.statusCode>=400)return t(r&&r.message);e(r)})),n&&s.upload.addEventListener("progress",(e=>{n(e)})),s.send(r)}))}const w={"image/gif":"gif","image/jpeg":"jpg","image/png":"png","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"};class _ extends e.Command{constructor(e){super(e),this._chosenAssets=new Set,this._wrapper=null,this._initListeners()}refresh(){this.value=this._getValue(),this.isEnabled=this._checkEnabled()}execute(){this.fire("ckbox:open")}_getValue(){return null!==this._wrapper}_checkEnabled(){const e=this.editor.commands.get("insertImage"),t=this.editor.commands.get("link");return!(!e.isEnabled&&!t.isEnabled)}_prepareOptions(){const e=this.editor.config.get("ckbox"),t=e.dialog,r=e.categories,n=e.view,o=e.upload;return{theme:e.theme,language:e.language,tokenUrl:e.tokenUrl,serviceOrigin:e.serviceOrigin,forceDemoLabel:e.forceDemoLabel,choosableFileExtensions:e.choosableFileExtensions,assets:{onChoose:e=>this.fire("ckbox:choose",e)},dialog:{onClose:()=>this.fire("ckbox:close"),width:t&&t.width,height:t&&t.height},categories:r&&{icons:r.icons},view:n&&{openLastView:n.openLastView,startupFolderId:n.startupFolderId,startupCategoryId:n.startupCategoryId,hideMaximizeButton:n.hideMaximizeButton},upload:o&&{componentsHideTimeout:o.componentsHideTimeout,dialogMinimizeTimeout:o.dialogMinimizeTimeout}}}_initListeners(){const e=this.editor,t=e.model,r=!e.config.get("ckbox.ignoreDataId");this.on("ckbox",(()=>{this.refresh()}),{priority:"low"}),this.on("ckbox:open",(()=>{this.isEnabled&&!this.value&&(this._wrapper=(0,s.createElement)(document,"div",{class:"ck ckbox-wrapper"}),document.body.appendChild(this._wrapper),window.CKBox.mount(this._wrapper,this._prepareOptions()))})),this.on("ckbox:close",(()=>{this.value&&(this._wrapper.remove(),this._wrapper=null,e.editing.view.focus())})),this.on("ckbox:choose",((n,o)=>{if(!this.isEnabled)return;const i=e.commands.get("insertImage"),s=e.commands.get("link"),a=function({assets:e,isImageAllowed:t,isLinkAllowed:r}){return e.map((e=>function(e){const t=e.data.metadata;if(!t)return!1;return t.width&&t.height}(e)?{id:e.data.id,type:"image",attributes:y(e)}:{id:e.data.id,type:"link",attributes:k(e)})).filter((e=>"image"===e.type?t:r))}({assets:o,isImageAllowed:i.isEnabled,isLinkAllowed:s.isEnabled}),c=a.length;0!==c&&(t.change((e=>{for(const t of a){const n=t===a[c-1],o=1===c;this._insertAsset(t,n,e,o),r&&(setTimeout((()=>this._chosenAssets.delete(t)),1e3),this._chosenAssets.add(t))}})),e.editing.view.focus())})),this.listenTo(e,"destroy",(()=>{this.fire("ckbox:close"),this._chosenAssets.clear()}))}_insertAsset(e,t,r,n){const o=this.editor.model.document.selection;r.removeSelectionAttribute("linkHref"),"image"===e.type?this._insertImage(e):this._insertLink(e,r,n),t||r.setSelection(o.getLastPosition())}_insertImage(e){const t=this.editor,{imageFallbackUrl:r,imageSources:n,imageTextAlternative:o,imageWidth:i,imageHeight:s,imagePlaceholder:a}=e.attributes;t.execute("insertImage",{source:{src:r,sources:n,alt:o,width:i,height:s,...a?{placeholder:a}:null}})}_insertLink(e,t,r){const n=this.editor,o=n.model,i=o.document.selection,{linkName:a,linkHref:c}=e.attributes;if(i.isCollapsed){const e=(0,s.toMap)(i.getAttributes()),l=t.createText(a,e);if(!r){const e=i.getLastPosition(),r=e.parent;"paragraph"===r.name&&r.isEmpty||n.execute("insertParagraph",{position:e});const s=o.insertContent(l);return t.setSelection(s),void n.execute("link",c)}const u=o.insertContent(l);t.setSelection(u)}n.execute("link",c)}}function y(e){const{imageFallbackUrl:t,imageSources:r}=b(e.data.imageUrls),{description:n,width:o,height:i,blurHash:s}=e.data.metadata,a=function(e){if(e)try{const t="32px",r=document.createElement("canvas");r.setAttribute("width",t),r.setAttribute("height",t);const n=r.getContext("2d");if(!n)return;const o=n.createImageData(32,32),i=f(e,32,32);return o.data.set(i),n.putImageData(o,0,0),r.toDataURL()}catch(e){return}}(s);return{imageFallbackUrl:t,imageSources:r,imageTextAlternative:n||"",imageWidth:o,imageHeight:i,...a?{imagePlaceholder:a}:null}}function k(e){return{linkName:e.data.name,linkHref:x(e)}}function x(e){const t=new URL(e.data.url);return t.searchParams.set("download","true"),t.toString()}var I=r(260);class A extends e.Plugin{static get pluginName(){return"CKBoxUtils"}static get isOfficialPlugin(){return!0}static get requires(){return["CloudServices"]}init(){const e=this.editor,t=!!e.config.get("ckbox"),r=!!window.CKBox;if(!t&&!r)return;e.config.define("ckbox",{serviceOrigin:"https://api.ckbox.io",defaultUploadCategories:null,ignoreDataId:!1,language:e.locale.uiLanguage,theme:"lark",tokenUrl:e.config.get("cloudServices.tokenUrl")});const n=e.plugins.get("CloudServices"),o=e.config.get("cloudServices.tokenUrl"),i=e.config.get("ckbox.tokenUrl");if(!i)throw new s.CKEditorError("ckbox-plugin-missing-token-url",this);this._token=i==o?Promise.resolve(n.token):n.registerTokenUrl(i)}getToken(){return this._token}async getWorkspaceId(){const e=(0,this.editor.t)("Cannot access default workspace."),t=this.editor.config.get("ckbox.defaultUploadWorkspaceId"),r=function(e,t){const[,r]=e.value.split("."),n=JSON.parse(atob(r)),o=n.auth&&n.auth.ckbox&&n.auth.ckbox.workspaces||[n.aud];return t?"superadmin"==(n.auth&&n.auth.ckbox&&n.auth.ckbox.role)||o.includes(t)?t:null:o[0]}(await this._token,t);if(null==r)throw(0,s.logError)("ckbox-access-default-workspace-error"),e;return r}async getCategoryIdForFile(e,t){const r=(0,this.editor.t)("Cannot determine a category for the uploaded file."),n=this.editor.config.get("ckbox.defaultUploadCategories"),o=this._getAvailableCategories(t),i="string"==typeof e?(s=await async function(e,t){try{const r=await fetch(e,{method:"HEAD",cache:"force-cache",...t});return r.ok&&r.headers.get("content-type")||""}catch{return""}}(e,t),w[s]):e.name.match(/\.(?<ext>[^.]+)$/).groups.ext.toLowerCase();var s;const a=await o;if(!a)throw r;if(n){const e=Object.keys(n).find((e=>n[e].find((e=>e.toLowerCase()==i))));if(e){const t=a.find((t=>t.id===e||t.name===e));if(!t)throw r;return t.id}}const c=a.find((e=>e.extensions.find((e=>e.toLowerCase()==i))));if(!c)throw r;return c.id}async _getAvailableCategories(e){const t=this.editor,r=this._token,{signal:n}=e,o=t.config.get("ckbox.serviceOrigin"),i=await this.getWorkspaceId();try{const e=[];let t,r=0;do{const n=await a(r);e.push(...n.items),t=n.totalCount-(r+50),r+=50}while(t>0);return e}catch{return n.throwIfAborted(),void(0,s.logError)("ckbox-fetch-category-http-error")}async function a(e){const t=new URL("categories",o);return t.searchParams.set("limit",String(50)),t.searchParams.set("offset",String(e)),t.searchParams.set("workspaceId",i),v({url:t,signal:n,authorization:(await r).value})}}}class E extends e.Plugin{static get requires(){return["ImageUploadEditing","ImageUploadProgress",I.FileRepository,O]}static get pluginName(){return"CKBoxUploadAdapter"}static get isOfficialPlugin(){return!0}async afterInit(){const e=this.editor,t=!!e.config.get("ckbox"),r=!!window.CKBox;if(!t&&!r)return;const n=e.plugins.get(I.FileRepository),o=e.plugins.get(A);n.createUploadAdapter=t=>new j(t,e,o);const i=!e.config.get("ckbox.ignoreDataId"),s=e.plugins.get("ImageUploadEditing");i&&s.on("uploadComplete",((t,{imageElement:r,data:n})=>{e.model.change((e=>{e.setAttribute("ckboxImageId",n.ckboxImageId,r)}))}))}}class j{constructor(e,t,r){this.loader=e,this.token=r.getToken(),this.ckboxUtils=r,this.editor=t,this.controller=new AbortController,this.serviceOrigin=t.config.get("ckbox.serviceOrigin")}async upload(){const e=this.ckboxUtils,t=this.editor.t,r=await this.loader.file,n=await e.getCategoryIdForFile(r,{signal:this.controller.signal}),o=new URL("assets",this.serviceOrigin),i=new FormData;o.searchParams.set("workspaceId",await e.getWorkspaceId()),i.append("categoryId",n),i.append("file",r);return v({method:"POST",url:o,data:i,onUploadProgress:e=>{e.lengthComputable&&(this.loader.uploadTotal=e.total,this.loader.uploaded=e.loaded)},signal:this.controller.signal,authorization:(await this.token).value}).then((async e=>{const t=b(e.imageUrls);return{ckboxImageId:e.id,default:t.imageFallbackUrl,sources:t.imageSources}})).catch((()=>{const e=t("Cannot upload file:")+` ${r.name}.`;return Promise.reject(e)}))}abort(){this.controller.abort()}}const C="NoPermission";class O extends e.Plugin{static get pluginName(){return"CKBoxEditing"}static get isOfficialPlugin(){return!0}static get requires(){return["LinkEditing","PictureEditing",E,A]}init(){const e=this.editor;this._shouldBeInitialised()&&(this._checkImagePlugins(),L()&&e.commands.add("ckbox",new _(e)),async function(e){const t=e.plugins.get(A),r=e.config.get("ckbox.serviceOrigin"),n=new URL("permissions",r),{value:o}=await t.getToken(),i=await v({url:n,authorization:o,signal:(new AbortController).signal});return Object.values(i).some((e=>e["asset:create"]))}(e).then((e=>{e||this._blockImageCommands()})))}afterInit(){const e=this.editor;this._shouldBeInitialised()&&(e.config.get("ckbox.ignoreDataId")||(this._initSchema(),this._initConversion(),this._initFixers()))}_shouldBeInitialised(){return!!this.editor.config.get("ckbox")||L()}_blockImageCommands(){const e=this.editor,t=e.commands.get("uploadImage"),r=e.commands.get("ckboxImageEdit");t&&(t.isAccessAllowed=!1,t.forceDisabled(C)),r&&r.forceDisabled(C)}_checkImagePlugins(){const e=this.editor;e.plugins.has("ImageBlockEditing")||e.plugins.has("ImageInlineEditing")||(0,s.logError)("ckbox-plugin-image-feature-missing",e)}_initSchema(){const e=this.editor.model.schema;e.extend("$text",{allowAttributes:"ckboxLinkId"}),e.isRegistered("imageBlock")&&e.extend("imageBlock",{allowAttributes:["ckboxImageId","ckboxLinkId"]}),e.isRegistered("imageInline")&&e.extend("imageInline",{allowAttributes:["ckboxImageId","ckboxLinkId"]}),e.addAttributeCheck((e=>{if(!e.last.getAttribute("linkHref"))return!1}),"ckboxLinkId")}_initConversion(){const e=this.editor;e.conversion.for("downcast").add((e=>{e.on("attribute:ckboxLinkId:imageBlock",((e,t,r)=>{const{writer:n,mapper:o,consumable:i}=r;if(!i.consume(t.item,e.name))return;const s=[...o.toViewElement(t.item).getChildren()].find((e=>"a"===e.name));s&&(t.item.hasAttribute("ckboxLinkId")?n.setAttribute("data-ckbox-resource-id",t.item.getAttribute("ckboxLinkId"),s):n.removeAttribute("data-ckbox-resource-id",s))}),{priority:"low"}),e.on("attribute:ckboxLinkId",((e,t,r)=>{const{writer:n,mapper:o,consumable:i}=r;if(i.consume(t.item,e.name)){if(t.attributeOldValue){const e=S(n,t.attributeOldValue);n.unwrap(o.toViewRange(t.range),e)}if(t.attributeNewValue){const e=S(n,t.attributeNewValue);if(t.item.is("selection")){const t=n.document.selection;n.wrap(t.getFirstRange(),e)}else n.wrap(o.toViewRange(t.range),e)}}}),{priority:"low"})})),e.conversion.for("upcast").add((e=>{e.on("element:a",((e,t,r)=>{const{writer:n,consumable:o}=r;if(!t.viewItem.getAttribute("href"))return;if(!o.consume(t.viewItem,{attributes:["data-ckbox-resource-id"]}))return;const i=t.viewItem.getAttribute("data-ckbox-resource-id");if(i)if(t.modelRange)for(let e of t.modelRange.getItems())e.is("$textProxy")&&(e=e.textNode),B(e)&&n.setAttribute("ckboxLinkId",i,e);else{const e=t.modelCursor.nodeBefore||t.modelCursor.parent;n.setAttribute("ckboxLinkId",i,e)}}),{priority:"low"})})),e.conversion.for("downcast").attributeToAttribute({model:"ckboxImageId",view:"data-ckbox-resource-id"}),e.conversion.for("upcast").elementToAttribute({model:{key:"ckboxImageId",value:e=>e.getAttribute("data-ckbox-resource-id")},view:{attributes:{"data-ckbox-resource-id":/[\s\S]+/}}});const t=e.commands.get("replaceImageSource");t&&this.listenTo(t,"cleanupImage",((e,[t,r])=>{t.removeAttribute("ckboxImageId",r)}))}_initFixers(){const e=this.editor,t=e.model,r=t.document.selection;t.document.registerPostFixer(function(e){return t=>{let r=!1;const n=e.model,o=e.commands.get("ckbox");if(!o)return r;for(const e of n.document.differ.getChanges()){if("insert"!==e.type&&"attribute"!==e.type)continue;const n="insert"===e.type?new i.Range(e.position,e.position.getShiftedBy(e.length)):e.range,s="attribute"===e.type&&"linkHref"===e.attributeKey&&null===e.attributeNewValue;for(const e of n.getItems()){if(s&&e.hasAttribute("ckboxLinkId")){t.removeAttribute("ckboxLinkId",e),r=!0;continue}const n=P(e,o._chosenAssets);for(const o of n){const n="image"===o.type?"ckboxImageId":"ckboxLinkId";o.id!==e.getAttribute(n)&&(t.setAttribute(n,o.id,e),r=!0)}}}return r}}(e)),t.document.registerPostFixer(function(e){return t=>!(e.hasAttribute("linkHref")||!e.hasAttribute("ckboxLinkId"))&&(t.removeSelectionAttribute("ckboxLinkId"),!0)}(r))}}function P(e,t){const r=e.is("element","imageInline")||e.is("element","imageBlock"),n=e.hasAttribute("linkHref");return[...t].filter((t=>"image"===t.type&&r?t.attributes.imageFallbackUrl===e.getAttribute("src"):"link"===t.type&&n?t.attributes.linkHref===e.getAttribute("linkHref"):void 0))}function S(e,t){const r=e.createAttributeElement("a",{"data-ckbox-resource-id":t},{priority:5});return e.setCustomProperty("link",!0,r),r}function B(e){return!!e.is("$text")||!(!e.is("element","imageInline")&&!e.is("element","imageBlock"))}function L(){return!!window.CKBox}class U extends e.Plugin{static get pluginName(){return"CKBox"}static get isOfficialPlugin(){return!0}static get requires(){return[O,o]}}const F=function(){this.__data__=[],this.size=0};const T=function(e,t){return e===t||e!=e&&t!=t};const M=function(e,t){for(var r=e.length;r--;)if(T(e[r][0],t))return r;return-1};var z=Array.prototype.splice;const R=function(e){var t=this.__data__,r=M(t,e);return!(r<0)&&(r==t.length-1?t.pop():z.call(t,r,1),--this.size,!0)};const V=function(e){var t=this.__data__,r=M(t,e);return r<0?void 0:t[r][1]};const N=function(e){return M(this.__data__,e)>-1};const K=function(e,t){var r=this.__data__,n=M(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this};function D(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}D.prototype.clear=F,D.prototype.delete=R,D.prototype.get=V,D.prototype.has=N,D.prototype.set=K;const H=D;const $=function(){this.__data__=new H,this.size=0};const q=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r};const W=function(e){return this.__data__.get(e)};const Z=function(e){return this.__data__.has(e)};const Y="object"==typeof global&&global&&global.Object===Object&&global;var G="object"==typeof self&&self&&self.Object===Object&&self;const J=Y||G||Function("return this")();const X=J.Symbol;var Q=Object.prototype,ee=Q.hasOwnProperty,te=Q.toString,re=X?X.toStringTag:void 0;const ne=function(e){var t=ee.call(e,re),r=e[re];try{e[re]=void 0;var n=!0}catch(e){}var o=te.call(e);return n&&(t?e[re]=r:delete e[re]),o};var oe=Object.prototype.toString;const ie=function(e){return oe.call(e)};var se=X?X.toStringTag:void 0;const ae=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":se&&se in Object(e)?ne(e):ie(e)};const ce=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};const le=function(e){if(!ce(e))return!1;var t=ae(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t};const ue=J["__core-js_shared__"];var de,ge=(de=/[^.]+$/.exec(ue&&ue.keys&&ue.keys.IE_PROTO||""))?"Symbol(src)_1."+de:"";const he=function(e){return!!ge&&ge in e};var pe=Function.prototype.toString;const me=function(e){if(null!=e){try{return pe.call(e)}catch(e){}try{return e+""}catch(e){}}return""};var fe=/^\[object .+?Constructor\]$/,be=Function.prototype,ve=Object.prototype,we=be.toString,_e=ve.hasOwnProperty,ye=RegExp("^"+we.call(_e).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const ke=function(e){return!(!ce(e)||he(e))&&(le(e)?ye:fe).test(me(e))};const xe=function(e,t){return null==e?void 0:e[t]};const Ie=function(e,t){var r=xe(e,t);return ke(r)?r:void 0};const Ae=Ie(J,"Map");const Ee=Ie(Object,"create");const je=function(){this.__data__=Ee?Ee(null):{},this.size=0};const Ce=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t};var Oe=Object.prototype.hasOwnProperty;const Pe=function(e){var t=this.__data__;if(Ee){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return Oe.call(t,e)?t[e]:void 0};var Se=Object.prototype.hasOwnProperty;const Be=function(e){var t=this.__data__;return Ee?void 0!==t[e]:Se.call(t,e)};const Le=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Ee&&void 0===t?"__lodash_hash_undefined__":t,this};function Ue(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Ue.prototype.clear=je,Ue.prototype.delete=Ce,Ue.prototype.get=Pe,Ue.prototype.has=Be,Ue.prototype.set=Le;const Fe=Ue;const Te=function(){this.size=0,this.__data__={hash:new Fe,map:new(Ae||H),string:new Fe}};const Me=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};const ze=function(e,t){var r=e.__data__;return Me(t)?r["string"==typeof t?"string":"hash"]:r.map};const Re=function(e){var t=ze(this,e).delete(e);return this.size-=t?1:0,t};const Ve=function(e){return ze(this,e).get(e)};const Ne=function(e){return ze(this,e).has(e)};const Ke=function(e,t){var r=ze(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this};function De(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}De.prototype.clear=Te,De.prototype.delete=Re,De.prototype.get=Ve,De.prototype.has=Ne,De.prototype.set=Ke;const He=De;const $e=function(e,t){var r=this.__data__;if(r instanceof H){var n=r.__data__;if(!Ae||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new He(n)}return r.set(e,t),this.size=r.size,this};function qe(e){var t=this.__data__=new H(e);this.size=t.size}qe.prototype.clear=$,qe.prototype.delete=q,qe.prototype.get=W,qe.prototype.has=Z,qe.prototype.set=$e;const We=qe;const Ze=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this};const Ye=function(e){return this.__data__.has(e)};function Ge(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new He;++t<r;)this.add(e[t])}Ge.prototype.add=Ge.prototype.push=Ze,Ge.prototype.has=Ye;const Je=Ge;const Xe=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1};const Qe=function(e,t){return e.has(t)};const et=function(e,t,r,n,o,i){var s=1&r,a=e.length,c=t.length;if(a!=c&&!(s&&c>a))return!1;var l=i.get(e),u=i.get(t);if(l&&u)return l==t&&u==e;var d=-1,g=!0,h=2&r?new Je:void 0;for(i.set(e,t),i.set(t,e);++d<a;){var p=e[d],m=t[d];if(n)var f=s?n(m,p,d,t,e,i):n(p,m,d,e,t,i);if(void 0!==f){if(f)continue;g=!1;break}if(h){if(!Xe(t,(function(e,t){if(!Qe(h,t)&&(p===e||o(p,e,r,n,i)))return h.push(t)}))){g=!1;break}}else if(p!==m&&!o(p,m,r,n,i)){g=!1;break}}return i.delete(e),i.delete(t),g};const tt=J.Uint8Array;const rt=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e,n){r[++t]=[n,e]})),r};const nt=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=e})),r};var ot=X?X.prototype:void 0,it=ot?ot.valueOf:void 0;const st=function(e,t,r,n,o,i,s){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!i(new tt(e),new tt(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return T(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var a=rt;case"[object Set]":var c=1&n;if(a||(a=nt),e.size!=t.size&&!c)return!1;var l=s.get(e);if(l)return l==t;n|=2,s.set(e,t);var u=et(a(e),a(t),n,o,i,s);return s.delete(e),u;case"[object Symbol]":if(it)return it.call(e)==it.call(t)}return!1};const at=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e};const ct=Array.isArray;const lt=function(e,t,r){var n=t(e);return ct(e)?n:at(n,r(e))};const ut=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var s=e[r];t(s,r,e)&&(i[o++]=s)}return i};const dt=function(){return[]};var gt=Object.prototype.propertyIsEnumerable,ht=Object.getOwnPropertySymbols;const pt=ht?function(e){return null==e?[]:(e=Object(e),ut(ht(e),(function(t){return gt.call(e,t)})))}:dt;const mt=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n};const ft=function(e){return null!=e&&"object"==typeof e};const bt=function(e){return ft(e)&&"[object Arguments]"==ae(e)};var vt=Object.prototype,wt=vt.hasOwnProperty,_t=vt.propertyIsEnumerable;const yt=bt(function(){return arguments}())?bt:function(e){return ft(e)&&wt.call(e,"callee")&&!_t.call(e,"callee")};const kt=function(){return!1};var xt="object"==typeof exports&&exports&&!exports.nodeType&&exports,It=xt&&"object"==typeof module&&module&&!module.nodeType&&module,At=It&&It.exports===xt?J.Buffer:void 0;const Et=(At?At.isBuffer:void 0)||kt;var jt=/^(?:0|[1-9]\d*)$/;const Ct=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&jt.test(e))&&e>-1&&e%1==0&&e<t};const Ot=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991};var Pt={};Pt["[object Float32Array]"]=Pt["[object Float64Array]"]=Pt["[object Int8Array]"]=Pt["[object Int16Array]"]=Pt["[object Int32Array]"]=Pt["[object Uint8Array]"]=Pt["[object Uint8ClampedArray]"]=Pt["[object Uint16Array]"]=Pt["[object Uint32Array]"]=!0,Pt["[object Arguments]"]=Pt["[object Array]"]=Pt["[object ArrayBuffer]"]=Pt["[object Boolean]"]=Pt["[object DataView]"]=Pt["[object Date]"]=Pt["[object Error]"]=Pt["[object Function]"]=Pt["[object Map]"]=Pt["[object Number]"]=Pt["[object Object]"]=Pt["[object RegExp]"]=Pt["[object Set]"]=Pt["[object String]"]=Pt["[object WeakMap]"]=!1;const St=function(e){return ft(e)&&Ot(e.length)&&!!Pt[ae(e)]};const Bt=function(e){return function(t){return e(t)}};var Lt="object"==typeof exports&&exports&&!exports.nodeType&&exports,Ut=Lt&&"object"==typeof module&&module&&!module.nodeType&&module,Ft=Ut&&Ut.exports===Lt&&Y.process,Tt=function(){try{var e=Ut&&Ut.require&&Ut.require("util").types;return e||Ft&&Ft.binding&&Ft.binding("util")}catch(e){}}();var Mt=Tt&&Tt.isTypedArray;const zt=Mt?Bt(Mt):St;var Rt=Object.prototype.hasOwnProperty;const Vt=function(e,t){var r=ct(e),n=!r&&yt(e),o=!r&&!n&&Et(e),i=!r&&!n&&!o&&zt(e),s=r||n||o||i,a=s?mt(e.length,String):[],c=a.length;for(var l in e)!t&&!Rt.call(e,l)||s&&("length"==l||o&&("offset"==l||"parent"==l)||i&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||Ct(l,c))||a.push(l);return a};var Nt=Object.prototype;const Kt=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Nt)};const Dt=function(e,t){return function(r){return e(t(r))}}(Object.keys,Object);var Ht=Object.prototype.hasOwnProperty;const $t=function(e){if(!Kt(e))return Dt(e);var t=[];for(var r in Object(e))Ht.call(e,r)&&"constructor"!=r&&t.push(r);return t};const qt=function(e){return null!=e&&Ot(e.length)&&!le(e)};const Wt=function(e){return qt(e)?Vt(e):$t(e)};const Zt=function(e){return lt(e,Wt,pt)};var Yt=Object.prototype.hasOwnProperty;const Gt=function(e,t,r,n,o,i){var s=1&r,a=Zt(e),c=a.length;if(c!=Zt(t).length&&!s)return!1;for(var l=c;l--;){var u=a[l];if(!(s?u in t:Yt.call(t,u)))return!1}var d=i.get(e),g=i.get(t);if(d&&g)return d==t&&g==e;var h=!0;i.set(e,t),i.set(t,e);for(var p=s;++l<c;){var m=e[u=a[l]],f=t[u];if(n)var b=s?n(f,m,u,t,e,i):n(m,f,u,e,t,i);if(!(void 0===b?m===f||o(m,f,r,n,i):b)){h=!1;break}p||(p="constructor"==u)}if(h&&!p){var v=e.constructor,w=t.constructor;v==w||!("constructor"in e)||!("constructor"in t)||"function"==typeof v&&v instanceof v&&"function"==typeof w&&w instanceof w||(h=!1)}return i.delete(e),i.delete(t),h};const Jt=Ie(J,"DataView");const Xt=Ie(J,"Promise");const Qt=Ie(J,"Set");const er=Ie(J,"WeakMap");var tr="[object Map]",rr="[object Promise]",nr="[object Set]",or="[object WeakMap]",ir="[object DataView]",sr=me(Jt),ar=me(Ae),cr=me(Xt),lr=me(Qt),ur=me(er),dr=ae;(Jt&&dr(new Jt(new ArrayBuffer(1)))!=ir||Ae&&dr(new Ae)!=tr||Xt&&dr(Xt.resolve())!=rr||Qt&&dr(new Qt)!=nr||er&&dr(new er)!=or)&&(dr=function(e){var t=ae(e),r="[object Object]"==t?e.constructor:void 0,n=r?me(r):"";if(n)switch(n){case sr:return ir;case ar:return tr;case cr:return rr;case lr:return nr;case ur:return or}return t});const gr=dr;var hr="[object Arguments]",pr="[object Array]",mr="[object Object]",fr=Object.prototype.hasOwnProperty;const br=function(e,t,r,n,o,i){var s=ct(e),a=ct(t),c=s?pr:gr(e),l=a?pr:gr(t),u=(c=c==hr?mr:c)==mr,d=(l=l==hr?mr:l)==mr,g=c==l;if(g&&Et(e)){if(!Et(t))return!1;s=!0,u=!1}if(g&&!u)return i||(i=new We),s||zt(e)?et(e,t,r,n,o,i):st(e,t,c,r,n,o,i);if(!(1&r)){var h=u&&fr.call(e,"__wrapped__"),p=d&&fr.call(t,"__wrapped__");if(h||p){var m=h?e.value():e,f=p?t.value():t;return i||(i=new We),o(m,f,r,n,i)}}return!!g&&(i||(i=new We),Gt(e,t,r,n,o,i))};const vr=function e(t,r,n,o,i){return t===r||(null==t||null==r||!ft(t)&&!ft(r)?t!=t&&r!=r:br(t,r,n,o,e,i))};const wr=function(e,t){return vr(e,t)};function _r(e){if(Array.isArray(e)){const t=e.map(_r);return e=>t.some((t=>t(e)))}if("origin"==e){const e=s.global.window.location.origin;return t=>new URL(t,s.global.document.baseURI).origin==e}if("function"==typeof e)return e;if(e instanceof RegExp)return t=>!(!t.match(e)&&!t.replace(/^https?:\/\//,"").match(e));return()=>!1}class yr extends e.Command{constructor(e){super(e),this._wrapper=null,this._processInProgress=new Set,this._updateUiDelayed=(0,s.delay)((()=>this.editor.ui.update()),0),this.value=!1,this._canEdit=function(e){const t=_r(e);return e=>!(!e.is("element","imageInline")&&!e.is("element","imageBlock"))&&(!!e.hasAttribute("ckboxImageId")||!!e.hasAttribute("src")&&t(e.getAttribute("src")))}(e.config.get("ckbox.allowExternalImagesEditing")),this._prepareOptions=(0,s.abortableDebounce)(((e,t)=>this._prepareOptionsAbortable(e,t))),this._prepareListeners()}refresh(){const e=this.editor;this.value=this._getValue();const t=e.model.document.selection.getSelectedElement();this.isEnabled=!!t&&this._canEdit(t)&&!this._checkIfElementIsBeingProcessed(t)}execute(){if(this._getValue())return;const e=(0,s.createElement)(document,"div",{class:"ck ckbox-wrapper"});this._wrapper=e,this.value=!0,document.body.appendChild(this._wrapper);const r={element:this.editor.model.document.selection.getSelectedElement(),controller:new AbortController};this._prepareOptions(r).then((t=>window.CKBox.mountImageEditor(e,t)),(e=>{const r=this.editor,n=r.t;r.plugins.get(t.Notification).showWarning(n("Failed to determine category of edited image."),{namespace:"ckbox"}),console.error(e),this._handleImageEditorClose()}))}destroy(){this._handleImageEditorClose(),this._prepareOptions.abort(),this._updateUiDelayed.cancel();for(const e of this._processInProgress.values())e.controller.abort();super.destroy()}_getValue(){return null!==this._wrapper}async _prepareOptionsAbortable(e,t){const r=this.editor,n=r.config.get("ckbox"),o=r.plugins.get(A),{element:i}=t;let s;const a=i.getAttribute("ckboxImageId");if(a)s={assetId:a};else{const t=new URL(i.getAttribute("src"),document.baseURI).href;s={imageUrl:t,uploadCategoryId:await o.getCategoryIdForFile(t,{signal:e})}}return{...s,imageEditing:{allowOverwrite:!1},tokenUrl:n.tokenUrl,...n.serviceOrigin&&{serviceOrigin:n.serviceOrigin},onClose:()=>this._handleImageEditorClose(),onSave:e=>this._handleImageEditorSave(t,e)}}_prepareListeners(){this.listenTo(this.editor.model.document,"change:data",(()=>{this._getProcessingStatesOfDeletedImages().forEach((e=>{e.controller.abort()}))}))}_getProcessingStatesOfDeletedImages(){const e=[];for(const t of this._processInProgress.values())"$graveyard"==t.element.root.rootName&&e.push(t);return e}_checkIfElementIsBeingProcessed(e){for(const{element:t}of this._processInProgress)if(wr(t,e))return!0;return!1}_handleImageEditorClose(){this._wrapper&&(this._wrapper.remove(),this._wrapper=null,this.editor.editing.view.focus(),this._updateUiDelayed(),this.refresh())}_handleImageEditorSave(r,n){const o=this.editor.locale.t,i=this.editor.plugins.get(t.Notification),a=this.editor.plugins.get(e.PendingActions),c=a.add(o("Processing the edited image."));this._processInProgress.add(r),this._showImageProcessingIndicator(r.element,n),this.refresh(),this._waitForAssetProcessed(n.data.id,r.controller.signal).then((e=>{this._replaceImage(r.element,e)}),(e=>{this.editor.editing.reconvertItem(r.element),r.controller.signal.aborted||(!e||e instanceof s.CKEditorError?i.showWarning(o("Server failed to process the image."),{namespace:"ckbox"}):console.error(e))})).finally((()=>{this._processInProgress.delete(r),a.remove(c),this.refresh()}))}async _getAssetStatusFromServer(e,t){const r=this.editor.plugins.get(A),n=new URL("assets/"+e,this.editor.config.get("ckbox.serviceOrigin")),o=await v({url:n,signal:t,authorization:(await r.getToken()).value}),i=o.metadata.metadataProcessingStatus;if(!i||"queued"==i)throw new s.CKEditorError("ckbox-image-not-processed");return{data:{...o}}}async _waitForAssetProcessed(e,t){const r=await(0,s.retry)((()=>this._getAssetStatusFromServer(e,t)),{signal:t,maxAttempts:5});if("success"!=r.data.metadata.metadataProcessingStatus)throw new s.CKEditorError("ckbox-image-processing-failed");return r}_showImageProcessingIndicator(e,t){const r=this.editor;r.editing.view.change((n=>{const o=r.editing.mapper.toViewElement(e),i=this.editor.plugins.get("ImageUtils").findViewImgElement(o);n.removeStyle("aspect-ratio",i),n.setAttribute("width",t.data.metadata.width,i),n.setAttribute("height",t.data.metadata.height,i),n.setStyle("width",`${t.data.metadata.width}px`,i),n.setStyle("height",`${t.data.metadata.height}px`,i),n.addClass("image-processing",o)}))}_replaceImage(e,t){const r=this.editor,{imageFallbackUrl:n,imageSources:o,imageWidth:i,imageHeight:s,imagePlaceholder:a}=y(t),c=Array.from(r.model.document.selection.getRanges());r.model.change((l=>{l.setSelection(e,"on"),r.execute("insertImage",{imageType:e.is("element","imageInline")?"imageInline":null,source:{src:n,sources:o,width:i,height:s,...a?{placeholder:a}:null,...e.hasAttribute("alt")?{alt:e.getAttribute("alt")}:null}});const u=e.getChildren();e=r.model.document.selection.getSelectedElement();for(const t of u)l.append(l.cloneElement(t),e);l.setAttribute("ckboxImageId",t.data.id,e),l.setSelection(c)}))}}class kr extends e.Plugin{static get pluginName(){return"CKBoxImageEditEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[O,A,e.PendingActions,t.Notification,"ImageUtils","ImageEditing"]}init(){const{editor:e}=this;e.commands.add("ckboxImageEdit",new yr(e))}}class xr extends e.Plugin{static get pluginName(){return"CKBoxImageEditUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor;e.ui.componentFactory.add("ckboxImageEdit",(r=>{const n=e.commands.get("ckboxImageEdit"),o=e.commands.get("uploadImage"),i=new t.ButtonView(r),s=r.t;return i.set({icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M1.201 1C.538 1 0 1.47 0 2.1v14.363c0 .64.534 1.037 1.186 1.037H5.06l5.058-5.078L6.617 9.15a.696.696 0 0 0-.957-.033L1.5 13.6V2.5h15v4.354a3.478 3.478 0 0 1 1.5.049V2.1c0-.63-.547-1.1-1.2-1.1H1.202Zm11.713 2.803a2.147 2.147 0 0 0-2.049 1.992 2.14 2.14 0 0 0 1.28 2.096 2.13 2.13 0 0 0 2.642-3.11 2.129 2.129 0 0 0-1.873-.978ZM8.089 17.635v2.388h2.389l7.046-7.046-2.39-2.39-7.045 7.048Zm11.282-6.507a.637.637 0 0 0 .139-.692.603.603 0 0 0-.139-.205l-1.49-1.488a.63.63 0 0 0-.899 0l-1.166 1.163 2.39 2.39 1.165-1.168Z"/></svg>',tooltip:!0}),i.bind("label").to(o,"isAccessAllowed",(e=>s(e?"Edit image":"You have no image editing permissions."))),i.bind("isOn").to(n,"value",n,"isEnabled",((e,t)=>e&&t)),i.bind("isEnabled").to(n),this.listenTo(i,"execute",(()=>{e.execute("ckboxImageEdit"),e.editing.view.focus()})),i}))}}var Ir=r(591),Ar=r.n(Ir),Er=r(639),jr=r.n(Er),Cr=r(128),Or=r.n(Cr),Pr=r(21),Sr=r.n(Pr),Br=r(51),Lr=r.n(Br),Ur=r(957),Fr={attributes:{"data-cke":!0}};Fr.setAttributes=Sr(),Fr.insert=Or().bind(null,"head"),Fr.domAPI=jr(),Fr.insertStyleElement=Lr();Ar()(Ur.A,Fr);Ur.A&&Ur.A.locals&&Ur.A.locals;class Tr extends e.Plugin{static get pluginName(){return"CKBoxImageEdit"}static get isOfficialPlugin(){return!0}static get requires(){return[kr,xr]}}})(),(window.CKEditor5=window.CKEditor5||{}).ckbox=n})();
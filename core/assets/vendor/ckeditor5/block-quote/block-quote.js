!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Block quote":"Block quote"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={28:(e,t,o)=>{"use strict";o.d(t,{A:()=>c});var n=o(758),r=o.n(n),i=o(935),s=o.n(i)()(r());s.push([e.id,".ck-content blockquote{border-left:5px solid #ccc;font-style:italic;margin-left:0;margin-right:0;overflow:hidden;padding-left:1.5em;padding-right:1.5em}.ck-content[dir=rtl] blockquote{border-left:0;border-right:5px solid #ccc}",""]);const c=s},935:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var o="",n=void 0!==t[5];return t[4]&&(o+="@supports (".concat(t[4],") {")),t[2]&&(o+="@media ".concat(t[2]," {")),n&&(o+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),o+=e(t),n&&(o+="}"),t[2]&&(o+="}"),t[4]&&(o+="}"),o})).join("")},t.i=function(e,o,n,r,i){"string"==typeof e&&(e=[[null,e,void 0]]);var s={};if(n)for(var c=0;c<this.length;c++){var a=this[c][0];null!=a&&(s[a]=!0)}for(var l=0;l<e.length;l++){var u=[].concat(e[l]);n&&s[u[0]]||(void 0!==i&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=i),o&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=o):u[2]=o),r&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=r):u[4]="".concat(r)),t.push(u))}},t}},758:e=>{"use strict";e.exports=function(e){return e[1]}},591:e=>{"use strict";var t=[];function o(e){for(var o=-1,n=0;n<t.length;n++)if(t[n].identifier===e){o=n;break}return o}function n(e,n){for(var i={},s=[],c=0;c<e.length;c++){var a=e[c],l=n.base?a[0]+n.base:a[0],u=i[l]||0,d="".concat(l," ").concat(u);i[l]=u+1;var f=o(d),p={css:a[1],media:a[2],sourceMap:a[3],supports:a[4],layer:a[5]};if(-1!==f)t[f].references++,t[f].updater(p);else{var m=r(p,n);n.byIndex=c,t.splice(c,0,{identifier:d,updater:m,references:1})}s.push(d)}return s}function r(e,t){var o=t.domAPI(t);o.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;o.update(e=t)}else o.remove()}}e.exports=function(e,r){var i=n(e=e||[],r=r||{});return function(e){e=e||[];for(var s=0;s<i.length;s++){var c=o(i[s]);t[c].references--}for(var a=n(e,r),l=0;l<i.length;l++){var u=o(i[l]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}i=a}}},128:e=>{"use strict";var t={};e.exports=function(e,o){var n=function(e){if(void 0===t[e]){var o=document.querySelector(e);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}t[e]=o}return t[e]}(e);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(o)}},51:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},21:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(o){e.setAttribute(o,t[o])}))}},639:e=>{"use strict";var t,o=(t=[],function(e,o){return t[e]=o,t.filter(Boolean).join("\n")});function n(e,t,n,r){var i;if(n)i="";else{i="",r.supports&&(i+="@supports (".concat(r.supports,") {")),r.media&&(i+="@media ".concat(r.media," {"));var s=void 0!==r.layer;s&&(i+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),i+=r.css,s&&(i+="}"),r.media&&(i+="}"),r.supports&&(i+="}")}if(e.styleSheet)e.styleSheet.cssText=o(t,i);else{var c=document.createTextNode(i),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(c,a[t]):e.appendChild(c)}}var r={singleton:null,singletonCounter:0};e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=r.singletonCounter++,o=r.singleton||(r.singleton=e.insertStyleElement(e));return{update:function(e){n(o,t,!1,e)},remove:function(e){n(o,t,!0,e)}}}},782:(e,t,o)=>{e.exports=o(237)("./src/core.js")},507:(e,t,o)=>{e.exports=o(237)("./src/enter.js")},834:(e,t,o)=>{e.exports=o(237)("./src/typing.js")},311:(e,t,o)=>{e.exports=o(237)("./src/ui.js")},584:(e,t,o)=>{e.exports=o(237)("./src/utils.js")},237:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function o(n){var r=t[n];if(void 0!==r)return r.exports;var i=t[n]={id:n,exports:{}};return e[n](i,i.exports,o),i.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";o.r(n),o.d(n,{BlockQuote:()=>S,BlockQuoteEditing:()=>u,BlockQuoteUI:()=>Q});var e=o(782),t=o(507),r=o(834),i=o(584);class s extends e.Command{refresh(){this.value=this._getValue(),this.isEnabled=this._checkEnabled()}execute(e={}){const t=this.editor.model,o=t.schema,n=t.document.selection,r=Array.from(n.getSelectedBlocks()),i=void 0===e.forceValue?!this.value:e.forceValue;t.change((e=>{if(i){const t=r.filter((e=>c(e)||l(o,e)));this._applyQuote(e,t)}else this._removeQuote(e,r.filter(c))}))}_getValue(){const e=this.editor.model.document.selection,t=(0,i.first)(e.getSelectedBlocks());return!(!t||!c(t))}_checkEnabled(){if(this.value)return!0;const e=this.editor.model.document.selection,t=this.editor.model.schema,o=(0,i.first)(e.getSelectedBlocks());return!!o&&l(t,o)}_removeQuote(e,t){a(e,t).reverse().forEach((t=>{if(t.start.isAtStart&&t.end.isAtEnd)return void e.unwrap(t.start.parent);if(t.start.isAtStart){const o=e.createPositionBefore(t.start.parent);return void e.move(t,o)}t.end.isAtEnd||e.split(t.end);const o=e.createPositionAfter(t.end.parent);e.move(t,o)}))}_applyQuote(e,t){const o=[];a(e,t).reverse().forEach((t=>{let n=c(t.start);n||(n=e.createElement("blockQuote"),e.wrap(t,n)),o.push(n)})),o.reverse().reduce(((t,o)=>t.nextSibling==o?(e.merge(e.createPositionAfter(t)),t):o))}}function c(e){return"blockQuote"==e.parent.name?e.parent:null}function a(e,t){let o,n=0;const r=[];for(;n<t.length;){const i=t[n],s=t[n+1];o||(o=e.createPositionBefore(i)),s&&i.nextSibling==s||(r.push(e.createRange(o,e.createPositionAfter(i))),o=null),n++}return r}function l(e,t){const o=e.checkChild(t.parent,"blockQuote"),n=e.checkChild(["$root","blockQuote"],t);return o&&n}class u extends e.Plugin{static get pluginName(){return"BlockQuoteEditing"}static get isOfficialPlugin(){return!0}static get requires(){return[t.Enter,r.Delete]}init(){const e=this.editor,t=e.model.schema;e.commands.add("blockQuote",new s(e)),t.register("blockQuote",{inheritAllFrom:"$container"}),e.conversion.elementToElement({model:"blockQuote",view:"blockquote"}),e.model.document.registerPostFixer((o=>{const n=e.model.document.differ.getChanges();for(const e of n)if("insert"==e.type){const n=e.position.nodeAfter;if(!n)continue;if(n.is("element","blockQuote")&&n.isEmpty)return o.remove(n),!0;if(n.is("element","blockQuote")&&!t.checkChild(e.position,n))return o.unwrap(n),!0;if(n.is("element")){const e=o.createRangeIn(n);for(const n of e.getItems())if(n.is("element","blockQuote")&&!t.checkChild(o.createPositionBefore(n),n))return o.unwrap(n),!0}}else if("remove"==e.type){const t=e.position.parent;if(t.is("element","blockQuote")&&t.isEmpty)return o.remove(t),!0}return!1}));const o=this.editor.editing.view.document,n=e.model.document.selection,r=e.commands.get("blockQuote");this.listenTo(o,"enter",((t,o)=>{if(!n.isCollapsed||!r.value)return;n.getLastPosition().parent.isEmpty&&(e.execute("blockQuote"),e.editing.view.scrollToTheSelection(),o.preventDefault(),t.stop())}),{context:"blockquote"}),this.listenTo(o,"delete",((t,o)=>{if("backward"!=o.direction||!n.isCollapsed||!r.value)return;const i=n.getLastPosition().parent;i.isEmpty&&!i.previousSibling&&(e.execute("blockQuote"),e.editing.view.scrollToTheSelection(),o.preventDefault(),t.stop())}),{context:"blockquote"})}}var d=o(311),f=o(591),p=o.n(f),m=o(639),h=o.n(m),v=o(128),g=o.n(v),b=o(21),k=o.n(b),y=o(51),x=o.n(y),w=o(28),E={attributes:{"data-cke":!0}};E.setAttributes=k(),E.insert=g().bind(null,"head"),E.domAPI=h(),E.insertStyleElement=x();p()(w.A,E);w.A&&w.A.locals&&w.A.locals;class Q extends e.Plugin{static get pluginName(){return"BlockQuoteUI"}static get isOfficialPlugin(){return!0}init(){const e=this.editor;e.ui.componentFactory.add("blockQuote",(()=>{const e=this._createButton(d.ButtonView);return e.set({tooltip:!0}),e})),e.ui.componentFactory.add("menuBar:blockQuote",(()=>{const e=this._createButton(d.MenuBarMenuListItemButtonView);return e.set({role:"menuitemcheckbox"}),e}))}_createButton(t){const o=this.editor,n=o.locale,r=o.commands.get("blockQuote"),i=new t(o.locale),s=n.t;return i.set({label:s("Block quote"),icon:e.icons.quote,isToggleable:!0}),i.bind("isEnabled").to(r,"isEnabled"),i.bind("isOn").to(r,"value"),this.listenTo(i,"execute",(()=>{o.execute("blockQuote"),o.editing.view.focus()})),i}}class S extends e.Plugin{static get requires(){return[u,Q]}static get pluginName(){return"BlockQuote"}static get isOfficialPlugin(){return!0}}})(),(window.CKEditor5=window.CKEditor5||{}).blockQuote=n})();
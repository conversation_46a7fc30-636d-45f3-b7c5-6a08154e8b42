!function(e){const t=e.bs=e.bs||{};t.dictionary=Object.assign(t.dictionary||{},{"(may require <kbd>Fn</kbd>)":"","%0 of %1":"%0 od %1",Accept:"",Accessibility:"","Accessibility help":"",Aquamarine:"","Below, you can find a list of keyboard shortcuts that can be used in the editor.":"",Black:"",Blue:"",Cancel:"Poništi","Cannot upload file:":"Nije moguće učitati fajl:",Clear:"","Click to edit block":"",Close:"","Close contextual balloons, dropdowns, and dialogs":"","Color picker":"","Content editing keystrokes":"","Copy selected content":"","Dim grey":"","Drag to move":"","Dropdown menu":"","Dropdown toolbar":"","Edit block":"","Editor block content toolbar":"","Editor contextual toolbar":"","Editor dialog":"","Editor menu bar":"","Editor toolbar":"","Execute the currently focused button. Executing buttons that interact with the editor content moves the focus back to the content.":"",File:"",Green:"",Grey:"","Help Contents. To close this dialog press ESC.":"",HEX:"","Insert a hard break (a new paragraph)":"","Insert a new paragraph directly after a widget":"","Insert a new paragraph directly before a widget":"","Insert a soft break (a <code>&lt;br&gt;</code> element)":"","Insert image with file manager":"","Insert paragraph after block":"","Insert paragraph before block":"","Insert with file manager":"","Keystrokes that can be used when a widget is selected (for example: image, table, etc.)":"","Light blue":"","Light green":"","Light grey":"",MENU_BAR_MENU_EDIT:"Uredi",MENU_BAR_MENU_FILE:"",MENU_BAR_MENU_FONT:"",MENU_BAR_MENU_FORMAT:"",MENU_BAR_MENU_HELP:"",MENU_BAR_MENU_INSERT:"Umetni",MENU_BAR_MENU_TEXT:"",MENU_BAR_MENU_TOOLS:"",MENU_BAR_MENU_VIEW:"","Move focus between form fields (inputs, buttons, etc.)":"","Move focus from an editable area back to the parent widget":"","Move focus in and out of an active dialog window":"","Move focus to the menu bar, navigate between menu bars":"","Move focus to the toolbar, navigate between toolbars":"","Move the caret to allow typing directly after a widget":"","Move the caret to allow typing directly before a widget":"","Navigate through the toolbar or menu bar":"",Next:"","No results found":"","No searchable items":"","Open the accessibility help dialog":"",Orange:"",Paragraph:"Paragraf","Paste content":"","Paste content as plain text":"",'Please enter a valid color (e.g. "ff0000").':"","Press %0 for help.":"","Press Enter to type after or press Shift + Enter to type before the widget":"",Previous:"",Purple:"",Red:"",Redo:"","Remove color":"Ukloni boju","Replace image with file manager":"","Replace with file manager":"","Restore default":"Vrati na zadano","Rich Text Editor":"","Rich Text Editor. Editing area: %0":"",Save:"Sačuvaj","Select all":"","Show more items":"Prikaži više stavki","These keyboard shortcuts allow for quick access to content editing features.":"","Toggle caption off":"","Toggle caption on":"",Turquoise:"",Undo:"","Upload in progress":"","Use the following keystrokes for more efficient navigation in the CKEditor 5 user interface.":"","User interface and content navigation keystrokes":"",White:"","Widget toolbar":"","With file manager":"",Yellow:""}),t.getPluralForm=function(e){return e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));
!function(e){const n=e.de=e.de||{};n.dictionary=Object.assign(n.dictionary||{},{"(may require <kbd>Fn</kbd>)":"(erfordert gegebenenfalls <kbd>Fn</kbd>)","%0 of %1":"%0 von %1",Accept:"Akzeptieren",Accessibility:"Bedienungshilfen","Accessibility help":"Hilfe zur Eingabe",Aquamarine:"Aquamarinblau","Below, you can find a list of keyboard shortcuts that can be used in the editor.":"Unten finden Sie eine Liste mit Tastenkombinationen, die im Editor benutzt werden können.",Black:"Schwarz",Blue:"Blau",Cancel:"Abbrechen","Cannot upload file:":"Die Datei kann nicht hochgeladen werden:",Clear:"Löschen","Click to edit block":"Zum Bearbeiten des Blocks klicken",Close:"<PERSON><PERSON><PERSON>ßen","Close contextual balloons, dropdowns, and dialogs":"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Dropdown-Menü und Dialoge schließen","Color picker":"Farbwähler","Content editing keystrokes":"Tastatureingaben zur Inhaltsverarbeitung","Copy selected content":"Markierten Inhalt kopieren","Dim grey":"Dunkelgrau","Drag to move":"Zum Verschieben ziehen","Dropdown menu":"Dropdown-Menü","Dropdown toolbar":"Dropdown-Liste Werkzeugleiste","Edit block":"Absatz bearbeiten","Editor block content toolbar":"Editor Blockinhalt-Toolbar","Editor contextual toolbar":"Editor kontextuelle Toolbar","Editor dialog":"Editor-Dialog","Editor menu bar":"Menüleiste des Editors","Editor toolbar":"Editor Werkzeugleiste","Execute the currently focused button. Executing buttons that interact with the editor content moves the focus back to the content.":"Ausführen der aktuell fokussierten Schaltfläche. Das Ausführen von Schaltflächen, die mit dem Inhalt des Editors interagieren, richtet den Fokus zurück auf den Inhalt.",File:"Datei",Green:"Grün",Grey:"Grau","Help Contents. To close this dialog press ESC.":"Hilfe zum Inhalt. Drücken Sie die Esc-Taste, um dieses Dialogfenster zu schließen.",HEX:"HEX","Insert a hard break (a new paragraph)":"Zeilenumbruch einfügen (einen neuen Abschnitt)","Insert a new paragraph directly after a widget":"Einen neuen Abschnitt direkt nach einem Widget einfügen","Insert a new paragraph directly before a widget":"Einen neuen Abschnitt direkt vor einem Widget einfügen","Insert a soft break (a <code>&lt;br&gt;</code> element)":"Einen weichen Zeilenumbruch einfügen (ein <code>&lt;br&gt;</code>-Element)","Insert image with file manager":"Bild mit dem Dateimanager einfügen","Insert paragraph after block":"Absatz nach Block einfügen","Insert paragraph before block":"Absatz vor Block einfügen","Insert with file manager":"Mit Dateimanager einfügen","Keystrokes that can be used when a widget is selected (for example: image, table, etc.)":"Tastatureingaben, die verwendet werden können, wenn ein Widget ausgewählt wurde (zum Beispiel: Bilder, Tabellen etc.)","Light blue":"Hellblau","Light green":"Hellgrün","Light grey":"Hellgrau",MENU_BAR_MENU_EDIT:"Bearbeiten",MENU_BAR_MENU_FILE:"Datei",MENU_BAR_MENU_FONT:"Schriftart",MENU_BAR_MENU_FORMAT:"Format",MENU_BAR_MENU_HELP:"Hilfe",MENU_BAR_MENU_INSERT:"Einfügen",MENU_BAR_MENU_TEXT:"Text",MENU_BAR_MENU_TOOLS:"Werkzeuge",MENU_BAR_MENU_VIEW:"Anzeigen","Move focus between form fields (inputs, buttons, etc.)":"Fokus zwischen Formularfeldern verschieben (Eingaben, Tastenfelder etc.)","Move focus from an editable area back to the parent widget":"Bewegen Sie den Fokus von einem bearbeitbaren Bereich zurück zum übergeordneten Widget","Move focus in and out of an active dialog window":"Fokus auf ein aktives Dialogfenster richten oder aufheben","Move focus to the menu bar, navigate between menu bars":"Fokus auf die Menüleiste richten, zwischen Menüleisten navigieren","Move focus to the toolbar, navigate between toolbars":"Fokus auf die Symbolleiste verschieben, zwischen den Symbolleisten navigieren","Move the caret to allow typing directly after a widget":"Verschieben Sie den Textcursor, um die direkte Eingabe nach dem Widget zu erlauben","Move the caret to allow typing directly before a widget":"Verschieben Sie den Textcursor, um die Eingabe direkt nach dem Widget zu erlauben","Navigate through the toolbar or menu bar":"Durch die Werkzeugleiste oder Menüleiste navigieren",Next:"Nächste","No results found":"Keine Ergebnisse gefunden","No searchable items":"Keine durchsuchbaren Elemente","Open the accessibility help dialog":"Den Dialog zur Eingabehilfe öffnen",Orange:"Orange",Paragraph:"Absatz","Paste content":"Inhalt einfügen","Paste content as plain text":"Inhalt als Klartext einfügen",'Please enter a valid color (e.g. "ff0000").':"Bitte geben Sie eine gültige Farbe ein (z. B. „ff0000“).","Press %0 for help.":"Drücken Sie %0 für Hilfe.","Press Enter to type after or press Shift + Enter to type before the widget":"Drücken Sie die Eingabetaste, um nach dem Widget zu tippen oder Shift + Eingabetaste, um vor dem Widget zu tippen.",Previous:"vorherige",Purple:"Violett",Red:"Rot",Redo:"Wiederherstellen","Remove color":"Farbe entfernen","Replace image with file manager":"Bild mittels Dateimanager ersetzen","Replace with file manager":"Mittels Dateimanager ersetzen","Restore default":"Standard wiederherstellen","Rich Text Editor":"Rich Text Editor","Rich Text Editor. Editing area: %0":"Rich Text Editor. Bearbeitungsbereich: %0",Save:"Speichern","Select all":"Alles auswählen","Show more items":"Mehr anzeigen","These keyboard shortcuts allow for quick access to content editing features.":"Diese Tastenkombinationen ermöglichen einen schnellen Zugang zu den Inhaltsverarbeitungsfunktionen.","Toggle caption off":"Tabellenüberschrift deaktivieren","Toggle caption on":"Tabellenüberschrift aktivieren",Turquoise:"Türkis",Undo:"Rückgängig","Upload in progress":"Upload läuft","Use the following keystrokes for more efficient navigation in the CKEditor 5 user interface.":"Verwenden Sie die folgenden Tastatureingaben für eine effizientere Navigation auf der CKEditor-5-Benutzeroberfläche.","User interface and content navigation keystrokes":"Benutzeroberfläche und Inhaltsnavigationstasten",White:"Weiß","Widget toolbar":"Widget Werkzeugleiste","With file manager":"Mit dem Dateimanager",Yellow:"Gelb"}),n.getPluralForm=function(e){return 1!=e}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));
!function(e){const t=e.tk=e.tk||{};t.dictionary=Object.assign(t.dictionary||{},{"(may require <kbd>Fn</kbd>)":"","%0 of %1":"%1-iň %0-i",Accept:"",Accessibility:"","Accessibility help":"",Aquamarine:"Akuamarin","Below, you can find a list of keyboard shortcuts that can be used in the editor.":"",Black:"Gara",Blue:"Gök",Cancel:"Ýatyr","Cannot upload file:":"Faýl ýükläp bolmady:",Clear:"","Click to edit block":"",Close:"","Close contextual balloons, dropdowns, and dialogs":"","Color picker":"Reňk sa<PERSON>laýjy","Content editing keystrokes":"","Copy selected content":"","Dim grey":"Goýy çal","Drag to move":"","Dropdown menu":"","Dropdown toolbar":"Açylýan gurallar paneli","Edit block":"Bloky redaktirläň","Editor block content toolbar":"","Editor contextual toolbar":"","Editor dialog":"","Editor menu bar":"","Editor toolbar":"Redaktor gurallar paneli","Execute the currently focused button. Executing buttons that interact with the editor content moves the focus back to the content.":"",File:"",Green:"Ýaşyl",Grey:"Çal","Help Contents. To close this dialog press ESC.":"",HEX:"","Insert a hard break (a new paragraph)":"","Insert a new paragraph directly after a widget":"","Insert a new paragraph directly before a widget":"","Insert a soft break (a <code>&lt;br&gt;</code> element)":"","Insert image with file manager":"","Insert paragraph after block":"Blokdan soň abzas goýuň","Insert paragraph before block":"Blokdan öň abzas goýuň","Insert with file manager":"","Keystrokes that can be used when a widget is selected (for example: image, table, etc.)":"","Light blue":"Açyk gök","Light green":"Açyk ýaşyl","Light grey":"Açyk çal",MENU_BAR_MENU_EDIT:"Redaktirläň",MENU_BAR_MENU_FILE:"",MENU_BAR_MENU_FONT:"",MENU_BAR_MENU_FORMAT:"",MENU_BAR_MENU_HELP:"",MENU_BAR_MENU_INSERT:"",MENU_BAR_MENU_TEXT:"",MENU_BAR_MENU_TOOLS:"",MENU_BAR_MENU_VIEW:"","Move focus between form fields (inputs, buttons, etc.)":"","Move focus from an editable area back to the parent widget":"","Move focus in and out of an active dialog window":"","Move focus to the menu bar, navigate between menu bars":"","Move focus to the toolbar, navigate between toolbars":"","Move the caret to allow typing directly after a widget":"","Move the caret to allow typing directly before a widget":"","Navigate through the toolbar or menu bar":"",Next:"Indiki","No results found":"","No searchable items":"","Open the accessibility help dialog":"",Orange:"Mämişi",Paragraph:"Abzas","Paste content":"","Paste content as plain text":"",'Please enter a valid color (e.g. "ff0000").':"","Press %0 for help.":"","Press Enter to type after or press Shift + Enter to type before the widget":"",Previous:"Öňki",Purple:"Gyrmyzy",Red:"Gyzyl",Redo:"Öňe gaýtar","Remove color":"Reňki aýyryň","Replace image with file manager":"","Replace with file manager":"","Restore default":"","Rich Text Editor":"Baý Tekst Redaktory","Rich Text Editor. Editing area: %0":"",Save:"Saklaň","Select all":"Ählisini saýla","Show more items":"Has köp zady görkeziň","These keyboard shortcuts allow for quick access to content editing features.":"","Toggle caption off":"","Toggle caption on":"",Turquoise:"Turkuaz",Undo:"Yza gaýtar","Upload in progress":"Ýüklemek dowam edýär","Use the following keystrokes for more efficient navigation in the CKEditor 5 user interface.":"","User interface and content navigation keystrokes":"",White:"Ak","Widget toolbar":"Widget gurallar paneli","With file manager":"",Yellow:"Sary"}),t.getPluralForm=function(e){return 1!=e}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));
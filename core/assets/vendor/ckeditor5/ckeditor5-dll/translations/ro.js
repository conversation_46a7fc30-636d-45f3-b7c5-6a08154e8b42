!function(e){const t=e.ro=e.ro||{};t.dictionary=Object.assign(t.dictionary||{},{"(may require <kbd>Fn</kbd>)":"(poate fi necesar să apăsați <kbd>Fn</kbd>)","%0 of %1":"%0 din %1",Accept:"Acceptă",Accessibility:"Accesibilitate","Accessibility help":"Ajutor pentru accesibilitate",Aquamarine:"Acvamarin","Below, you can find a list of keyboard shortcuts that can be used in the editor.":"Mai jos puteți găsi o listă de comenzi rapide de tastatură care pot fi utilizate în editor.",Black:"Negru",Blue:"Albastru",Cancel:"Anulare","Cannot upload file:":"Nu se poate încărca fișierul:",Clear:"Ștergere","Click to edit block":"Faceți clic pentru a edita întreg blocul",Close:"Înch<PERSON><PERSON><PERSON>","Close contextual balloons, dropdowns, and dialogs":"Închide baloanele contextuale, ferestrele derulante și ferestrele de dialog","Color picker":"Alegere culoare","Content editing keystrokes":"Comenzi din tastatură pentru editarea conținutului","Copy selected content":"Copiază conținutul selectat","Dim grey":"Gri slab","Drag to move":"Glisați pentru a muta","Dropdown menu":"Meniu derulant","Dropdown toolbar":"Bară listă opțiuni","Edit block":"Editează bloc","Editor block content toolbar":"Bară de instrumente editor pentru blocuri de conținut","Editor contextual toolbar":"Bară contextuală de instrumente editor","Editor dialog":"Dialog editor","Editor menu bar":"Bara de meniuri a editorului","Editor toolbar":"Bară editor","Execute the currently focused button. Executing buttons that interact with the editor content moves the focus back to the content.":"Execută butonul focalizat în prezent. Executarea butoanelor care interacționează cu conținutul editorului mută focalizarea înapoi pe conținut.",File:"Fișier",Green:"Verde",Grey:"Gri","Help Contents. To close this dialog press ESC.":"Conținutul de asistență. Apăsați ESC pentru a închide acest dialog.",HEX:"HEX","Insert a hard break (a new paragraph)":"Introduce un capăt de rând obligatoriu (alineat nou)","Insert a new paragraph directly after a widget":"Inserează un nou paragraf direct după un widget","Insert a new paragraph directly before a widget":"Inserează un nou paragraf direct înaintea unui widget","Insert a soft break (a <code>&lt;br&gt;</code> element)":"Introduce capăt de rând opțional (un element <code>&lt;br&gt;</code>)","Insert image with file manager":"Inserare imagine cu managerul de fișiere","Insert paragraph after block":"Inserează un paragraf după bloc","Insert paragraph before block":"Inserează un paragraf înaintea blocului","Insert with file manager":"Inserare cu managerul de fișiere","Keystrokes that can be used when a widget is selected (for example: image, table, etc.)":"Comenzi din tastatură care pot fi utilizate atunci când este selectat un widget (de exemplu: imagine, tabel etc.)","Light blue":"Albastru deschis","Light green":"Verde deschis","Light grey":"Gri deschis",MENU_BAR_MENU_EDIT:"Editează",MENU_BAR_MENU_FILE:"Fișier",MENU_BAR_MENU_FONT:"Font",MENU_BAR_MENU_FORMAT:"Formatare",MENU_BAR_MENU_HELP:"Ajutor",MENU_BAR_MENU_INSERT:"Inserează",MENU_BAR_MENU_TEXT:"Text",MENU_BAR_MENU_TOOLS:"Instrumente",MENU_BAR_MENU_VIEW:"Vizualizare","Move focus between form fields (inputs, buttons, etc.)":"Schimbă elementul activ între câmpurile unui formular (câmpuri de introducere text, butoane etc.)","Move focus from an editable area back to the parent widget":"Mutați centrul de interes dintr-o zonă editabilă înapoi la widgetul părinte","Move focus in and out of an active dialog window":"Comutează focalizarea într-o fereastră de dialog activă și în afara acesteia","Move focus to the menu bar, navigate between menu bars":"Transferarea focusului pe bara de meniu, navigarea între barele de meniu","Move focus to the toolbar, navigate between toolbars":"Mută focalizarea pe bara de instrumente, navighează prin barele de instrumente","Move the caret to allow typing directly after a widget":"Mută cursorul pentru a permite tastarea direct după un widget","Move the caret to allow typing directly before a widget":"Mută cursorul pentru a permite tastarea direct înaintea unui widget","Navigate through the toolbar or menu bar":"Navigare prin bara de instrumente sau bara de meniuri",Next:"Înainte","No results found":"Nu au fost găsite rezultate","No searchable items":"Nu există elemente ce pot fi căutate","Open the accessibility help dialog":"Deschide fereastra de ajutor pentru accesibilitate",Orange:"Portocaliu",Paragraph:"Paragraf","Paste content":"Lipește conținut","Paste content as plain text":"Lipește conținutul ca text simplu",'Please enter a valid color (e.g. "ff0000").':"Vă rugăm să introduceți un cod de culoare valid (de ex., „ff0000”).","Press %0 for help.":"Apăsați %0] pentru ajutor.","Press Enter to type after or press Shift + Enter to type before the widget":"Apăsați Enter pentru a scrie după widget sau Shift+Enter pentru a scrie înaintea acestuia",Previous:"Înapoi",Purple:"Violet",Red:"Roșu",Redo:"Revenire","Remove color":"Șterge culoare","Replace image with file manager":"Înlocuire imagine cu managerul de fișiere","Replace with file manager":"Înlocuire cu managerul de fișiere","Restore default":"Reface la default","Rich Text Editor":"Editor de text","Rich Text Editor. Editing area: %0":"Editor Rich Text. Zonă editare: %0",Save:"Salvare","Select all":"Selectează-le pe toate","Show more items":"Arată mai multe elemente","These keyboard shortcuts allow for quick access to content editing features.":"Aceste comenzi rapide din tastatură permit accesul rapid la funcțiile de editare a conținutului.","Toggle caption off":"Dezactivați subtitlul","Toggle caption on":"Activați subtitlul",Turquoise:"Turcoaz",Undo:"Anulare","Upload in progress":"Încărcare în curs","Use the following keystrokes for more efficient navigation in the CKEditor 5 user interface.":"Utilizați următoarele comenzi din tastatură pentru o navigare mai eficientă în interfața cu utilizatorul CKEditor 5.","User interface and content navigation keystrokes":"Interfața cu utilizatorul și comenzi din tastatură pentru navigare în conținut",White:"Alb","Widget toolbar":"Bară widget","With file manager":"Cu managerul de fișiere",Yellow:"Galben"}),t.getPluralForm=function(e){return 1==e?0:0==e||e%100>0&&e%100<20?1:2}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));
!function(e){const t=e.ru=e.ru||{};t.dictionary=Object.assign(t.dictionary||{},{"(may require <kbd>Fn</kbd>)":"(может требовать <kbd>Fn</kbd>)","%0 of %1":"%0 из %1",Accept:"Принять",Accessibility:"Доступность","Accessibility help":"Помощь по специальным возможностям",Aquamarine:"Аквамариновый","Below, you can find a list of keyboard shortcuts that can be used in the editor.":"Ниже вы можете найти список сочетаний клавиш, которые можно использовать в редакторе.",Black:"Чёрный",Blue:"Синий",Cancel:"Отме<PERSON>","Cannot upload file:":"Невозможно загрузить файл",Clear:"Очистить","Click to edit block":"Нажмите, чтобы редактировать блок",Close:"Закрыть","Close contextual balloons, dropdowns, and dialogs":"Закрыть контекстные всплывающие окна, раскрывающиеся списки и диалоговые окна.","Color picker":"Выбор цвета","Content editing keystrokes":"Клавиши для редактирования контента","Copy selected content":"Копировать выбранное содержание","Dim grey":"Тёмно-серый","Drag to move":"Перетащить","Dropdown menu":"Раскрывающееся меню","Dropdown toolbar":"Выпадающая панель инструментов","Edit block":"Редактировать блок","Editor block content toolbar":"Панель инструментов редактора","Editor contextual toolbar":"Контекстуальная панель инструментов редактора","Editor dialog":"Диалоговое окно редактора","Editor menu bar":"Панель меню редактора","Editor toolbar":"Панель инструментов редактора","Execute the currently focused button. Executing buttons that interact with the editor content moves the focus back to the content.":"Активировать кнопку, находящуюся в фокусе. Активирование кнопок, которые взаимодействуют с содержимым редактора, перемещает фокус обратно на содержимое.",File:"Файл",Green:"Зелёный",Grey:"Серый","Help Contents. To close this dialog press ESC.":"Содержание справки. Чтобы закрыть это диалоговое окно, нажмите ESC.",HEX:"HEX","Insert a hard break (a new paragraph)":"Вставить жесткий разрыв (новый абзац)","Insert a new paragraph directly after a widget":"Вставить новый абзац непосредственно после виджета.","Insert a new paragraph directly before a widget":"Вставить новый абзац непосредственно перед виджетом.","Insert a soft break (a <code>&lt;br&gt;</code> element)":"Вставить мягкий разрыв (элемент <code>&lt;br&gt;</code>)","Insert image with file manager":"Вставить изображение с помощью файлового менеджера","Insert paragraph after block":"Вставить параграф после блока","Insert paragraph before block":"Вставить параграф перед блоком","Insert with file manager":"Вставка с помощью файлового менеджера","Keystrokes that can be used when a widget is selected (for example: image, table, etc.)":"Нажатия клавиш, которые можно использовать при выборе виджета (например: изображение, таблица и т. д.)","Light blue":"Голубой","Light green":"Салатовый","Light grey":"Светло-серый",MENU_BAR_MENU_EDIT:"Редактировать",MENU_BAR_MENU_FILE:"Файл",MENU_BAR_MENU_FONT:"Шрифт",MENU_BAR_MENU_FORMAT:"Формат",MENU_BAR_MENU_HELP:"Помощь",MENU_BAR_MENU_INSERT:"Вставить",MENU_BAR_MENU_TEXT:"Текст",MENU_BAR_MENU_TOOLS:"Инструменты",MENU_BAR_MENU_VIEW:"Посмотреть","Move focus between form fields (inputs, buttons, etc.)":"Переместить фокус между полями формы (вводы, кнопки и т. д.)","Move focus from an editable area back to the parent widget":"Переместите фокус с редактируемой области обратно на родительский виджет","Move focus in and out of an active dialog window":"Переместить фокус в активное диалоговое окно и обратно.","Move focus to the menu bar, navigate between menu bars":"Переместить фокус на панель меню, перемещаться между панелями меню","Move focus to the toolbar, navigate between toolbars":"Переместить фокус на панель инструментов, перемещаться между панелями инструментов","Move the caret to allow typing directly after a widget":"Переместить курсор, чтобы можно было вводить текст сразу после виджета.","Move the caret to allow typing directly before a widget":"Переместите курсор, чтобы можно было вводить текст непосредственно перед виджетом.","Navigate through the toolbar or menu bar":"Перемещение по панели инструментов или панели меню",Next:"Следующий","No results found":"Результаты не найдены","No searchable items":"Нет элементов для поиска","Open the accessibility help dialog":"Открыть диалоговое окно справки по специальным возможностям",Orange:"Оранжевый",Paragraph:"Параграф","Paste content":"Вставить содержание","Paste content as plain text":"Вставить содержанрие в виде обычного текста",'Please enter a valid color (e.g. "ff0000").':'Введите действительный цвет (например, "ff0000").',"Press %0 for help.":"Нажмите %0 для получения помощи.","Press Enter to type after or press Shift + Enter to type before the widget":"Нажмите Enter для ввода текста после виджета или нажмите Shift + Enter для ввода текста перед виджетом",Previous:"Предыдущий",Purple:"Фиолетовый",Red:"Красный",Redo:"Повторить","Remove color":"Убрать цвет","Replace image with file manager":"Заменить изображение с помощью файлового менеджера","Replace with file manager":"Заменить с помощью файлового менеджера","Restore default":"По умолчанию","Rich Text Editor":"Редактор","Rich Text Editor. Editing area: %0":"Редактор форматированного текста. Область редактирования: %0",Save:"Сохранить","Select all":"Выбрать все","Show more items":"Другие инструменты","These keyboard shortcuts allow for quick access to content editing features.":"Эти сочетания клавиш обеспечивают быстрый доступ к функциям редактирования контента.","Toggle caption off":"Выключить описание","Toggle caption on":"Включить описание",Turquoise:"Бирюзовый",Undo:"Отменить","Upload in progress":"Идёт загрузка","Use the following keystrokes for more efficient navigation in the CKEditor 5 user interface.":"Использовать следующие сочетания клавиш для более эффективной навигации в пользовательском интерфейсе CKEditor 5.","User interface and content navigation keystrokes":"Пользовательский интерфейс и клавиши навигации по контенту",White:"Белый","Widget toolbar":"Панель инструментов виджета","With file manager":"Из менеджера файлов",Yellow:"Жёлтый"}),t.getPluralForm=function(e){return e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));
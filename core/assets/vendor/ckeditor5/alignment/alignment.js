!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Align center":"Align center","Align left":"Align left","Align right":"Align right",Justify:"Justify","Text alignment":"Text alignment","Text alignment toolbar":"Text alignment toolbar"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={782:(e,t,n)=>{e.exports=n(237)("./src/core.js")},311:(e,t,n)=>{e.exports=n(237)("./src/ui.js")},584:(e,t,n)=>{e.exports=n(237)("./src/utils.js")},237:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function n(i){var o=t[i];if(void 0!==o)return o.exports;var a=t[i]={exports:{}};return e[i](a,a.exports,n),a.exports}n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{"use strict";n.r(i),n.d(i,{Alignment:()=>f,AlignmentEditing:()=>u,AlignmentUI:()=>m});var e=n(782),t=n(584);const o=["left","right","center","justify"];function a(e){return o.includes(e)}function r(e,t){return"rtl"==t.contentLanguageDirection?"right"===e:"left"===e}function s(e){const n=e.map((e=>{let t;return t="string"==typeof e?{name:e}:e,t})).filter((e=>{const n=o.includes(e.name);return n||(0,t.logWarning)("alignment-config-name-not-recognized",{option:e}),n})),i=n.filter((e=>Boolean(e.className))).length;if(i&&i<n.length)throw new t.CKEditorError("alignment-config-classnames-are-missing",{configuredOptions:e});return n.forEach(((n,i,o)=>{const a=o.slice(i+1);if(a.some((e=>e.name==n.name)))throw new t.CKEditorError("alignment-config-name-already-defined",{option:n,configuredOptions:e});if(n.className){if(a.some((e=>e.className==n.className)))throw new t.CKEditorError("alignment-config-classname-already-defined",{option:n,configuredOptions:e})}})),n}const l="alignment";class c extends e.Command{refresh(){const e=this.editor.locale,n=(0,t.first)(this.editor.model.document.selection.getSelectedBlocks());this.isEnabled=Boolean(n)&&this._canBeAligned(n),this.isEnabled&&n.hasAttribute("alignment")?this.value=n.getAttribute("alignment"):this.value="rtl"===e.contentLanguageDirection?"right":"left"}execute(e={}){const t=this.editor,n=t.locale,i=t.model,o=i.document,a=e.value;i.change((e=>{const t=Array.from(o.selection.getSelectedBlocks()).filter((e=>this._canBeAligned(e))),i=t[0].getAttribute("alignment");r(a,n)||i===a||!a?function(e,t){for(const n of e)t.removeAttribute(l,n)}(t,e):function(e,t,n){for(const i of e)t.setAttribute(l,n,i)}(t,e,a)}))}_canBeAligned(e){return this.editor.model.schema.checkAttribute(e,l)}}class u extends e.Plugin{static get pluginName(){return"AlignmentEditing"}static get isOfficialPlugin(){return!0}constructor(e){super(e),e.config.define("alignment",{options:o.map((e=>({name:e})))})}init(){const e=this.editor,t=e.locale,n=e.model.schema,i=s(e.config.get("alignment.options")).filter((e=>a(e.name)&&!r(e.name,t))),o=i.some((e=>!!e.className));n.extend("$block",{allowAttributes:"alignment"}),e.model.schema.setAttributeProperties("alignment",{isFormatting:!0}),o?e.conversion.attributeToAttribute(function(e){const t={};for(const n of e)t[n.name]={key:"class",value:n.className};const n={model:{key:"alignment",values:e.map((e=>e.name))},view:t};return n}(i)):e.conversion.for("downcast").attributeToAttribute(function(e){const t={};for(const{name:n}of e)t[n]={key:"style",value:{"text-align":n}};const n={model:{key:"alignment",values:e.map((e=>e.name))},view:t};return n}(i));const l=function(e){const t=[];for(const{name:n}of e)t.push({view:{key:"style",value:{"text-align":n}},model:{key:"alignment",value:n}});return t}(i);for(const t of l)e.conversion.for("upcast").attributeToAttribute(t);const u=function(e){const t=[];for(const{name:n}of e)t.push({view:{key:"align",value:n},model:{key:"alignment",value:n}});return t}(i);for(const t of u)e.conversion.for("upcast").attributeToAttribute(t);e.commands.add("alignment",new c(e))}}var g=n(311);const d=(()=>new Map([["left",e.icons.alignLeft],["right",e.icons.alignRight],["center",e.icons.alignCenter],["justify",e.icons.alignJustify]]))();class m extends e.Plugin{get localizedOptionTitles(){const e=this.editor.t;return{left:e("Align left"),right:e("Align right"),center:e("Align center"),justify:e("Justify")}}static get pluginName(){return"AlignmentUI"}static get isOfficialPlugin(){return!0}init(){const e=s(this.editor.config.get("alignment.options"));e.map((e=>e.name)).filter(a).forEach((e=>this._addButton(e))),this._addToolbarDropdown(e),this._addMenuBarMenu(e)}_addButton(e){this.editor.ui.componentFactory.add(`alignment:${e}`,(t=>this._createButton(t,e)))}_createButton(e,t,n={}){const i=this.editor,o=i.commands.get("alignment"),a=new g.ButtonView(e);return a.set({label:this.localizedOptionTitles[t],icon:d.get(t),tooltip:!0,isToggleable:!0,...n}),a.bind("isEnabled").to(o),a.bind("isOn").to(o,"value",(e=>e===t)),this.listenTo(a,"execute",(()=>{i.execute("alignment",{value:t}),i.editing.view.focus()})),a}_addToolbarDropdown(e){const t=this.editor;t.ui.componentFactory.add("alignment",(n=>{const i=(0,g.createDropdown)(n),o="rtl"===n.uiLanguageDirection?"w":"e",a=n.t;(0,g.addToolbarToDropdown)(i,(()=>e.map((e=>this._createButton(n,e.name,{tooltipPosition:o})))),{enableActiveItemFocusOnDropdownOpen:!0,isVertical:!0,ariaLabel:a("Text alignment toolbar")}),i.buttonView.set({label:a("Text alignment"),tooltip:!0}),i.extendTemplate({attributes:{class:"ck-alignment-dropdown"}});const r="rtl"===n.contentLanguageDirection?d.get("right"):d.get("left"),s=t.commands.get("alignment");return i.buttonView.bind("icon").to(s,"value",(e=>d.get(e)||r)),i.bind("isEnabled").to(s,"isEnabled"),this.listenTo(i,"execute",(()=>{t.editing.view.focus()})),i}))}_addMenuBarMenu(e){const t=this.editor;t.ui.componentFactory.add("menuBar:alignment",(n=>{const i=t.commands.get("alignment"),o=n.t,a=new g.MenuBarMenuView(n),r=new g.MenuBarMenuListView(n);a.bind("isEnabled").to(i),r.set({ariaLabel:o("Text alignment"),role:"menu"}),a.buttonView.set({label:o("Text alignment")});for(const o of e){const e=new g.MenuBarMenuListItemView(n,a),s=new g.MenuBarMenuListItemButtonView(n);s.delegate("execute").to(a),s.set({label:this.localizedOptionTitles[o.name],icon:d.get(o.name),role:"menuitemcheckbox",isToggleable:!0}),s.on("execute",(()=>{t.execute("alignment",{value:o.name}),t.editing.view.focus()})),s.bind("isOn").to(i,"value",(e=>e===o.name)),s.bind("isEnabled").to(i,"isEnabled"),e.children.add(s),r.items.add(e)}return a.panelView.children.add(r),a}))}}class f extends e.Plugin{static get requires(){return[u,m]}static get pluginName(){return"Alignment"}static get isOfficialPlugin(){return!0}}})(),(window.CKEditor5=window.CKEditor5||{}).alignment=i})();
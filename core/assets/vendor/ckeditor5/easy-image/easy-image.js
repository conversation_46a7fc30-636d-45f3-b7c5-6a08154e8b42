/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={782:(e,t,r)=>{e.exports=r(237)("./src/core.js")},260:(e,t,r)=>{e.exports=r(237)("./src/upload.js")},584:(e,t,r)=>{e.exports=r(237)("./src/utils.js")},237:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function r(i){var a=t[i];if(void 0!==a)return a.exports;var o=t[i]={exports:{}};return e[i](o,o.exports,r),o.exports}r.d=(e,t)=>{for(var i in t)r.o(t,i)&&!r.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{"use strict";r.r(i),r.d(i,{CloudServicesUploadAdapter:()=>o,EasyImage:()=>l});var e=r(782),t=r(584),a=r(260);class o extends e.Plugin{static get pluginName(){return"CloudServicesUploadAdapter"}static get isOfficialPlugin(){return!0}static get requires(){return["CloudServices",a.FileRepository]}init(){const e=this.editor,t=e.plugins.get("CloudServices"),r=t.token,i=t.uploadUrl;if(!r)return;const o=e.plugins.get("CloudServicesCore");this._uploadGateway=o.createUploadGateway(r,i),e.plugins.get(a.FileRepository).createUploadAdapter=e=>new s(this._uploadGateway,e)}}class s{constructor(e,t){this.uploadGateway=e,this.loader=t}upload(){return this.loader.file.then((e=>(this.fileUploader=this.uploadGateway.upload(e),this.fileUploader.on("progress",((e,t)=>{this.loader.uploadTotal=t.total,this.loader.uploaded=t.uploaded})),this.fileUploader.send())))}abort(){this.fileUploader.abort()}}class l extends e.Plugin{static get pluginName(){return"EasyImage"}static get isOfficialPlugin(){return!0}static get requires(){return[o,"ImageUpload"]}init(){const e=this.editor;e.plugins.has("ImageBlockEditing")||e.plugins.has("ImageInlineEditing")||(0,t.logWarning)("easy-image-image-feature-missing",e)}}})(),(window.CKEditor5=window.CKEditor5||{}).easyImage=i})();
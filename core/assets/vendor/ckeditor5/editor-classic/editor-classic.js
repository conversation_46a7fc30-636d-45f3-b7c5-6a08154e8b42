/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={662:(t,e,o)=>{"use strict";o.d(e,{A:()=>c});var i=o(758),n=o.n(i),r=o(935),s=o.n(r)()(n());s.push([t.id,".ck.ck-editor{position:relative}.ck.ck-editor .ck-editor__top .ck-sticky-panel .ck-toolbar{z-index:var(--ck-z-panel)}.ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content{border:solid var(--ck-color-base-border);border-radius:0;border-width:1px 1px 0}.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content,.ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content.ck-rounded-corners{border-radius:var(--ck-border-radius);border-bottom-left-radius:0;border-bottom-right-radius:0}.ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content.ck-sticky-panel__content_sticky{border-bottom-width:1px}.ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content .ck-menu-bar{border:0;border-bottom:1px solid var(--ck-color-base-border)}.ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content .ck-toolbar{border:0}.ck.ck-editor__main>.ck-editor__editable{background:var(--ck-color-base-background);border-radius:0}.ck-rounded-corners .ck.ck-editor__main>.ck-editor__editable,.ck.ck-editor__main>.ck-editor__editable.ck-rounded-corners{border-radius:var(--ck-border-radius);border-top-left-radius:0;border-top-right-radius:0}.ck.ck-editor__main>.ck-editor__editable:not(.ck-focused){border-color:var(--ck-color-base-border)}",""]);const c=s},935:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var o="",i=void 0!==e[5];return e[4]&&(o+="@supports (".concat(e[4],") {")),e[2]&&(o+="@media ".concat(e[2]," {")),i&&(o+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),o+=t(e),i&&(o+="}"),e[2]&&(o+="}"),e[4]&&(o+="}"),o})).join("")},e.i=function(t,o,i,n,r){"string"==typeof t&&(t=[[null,t,void 0]]);var s={};if(i)for(var c=0;c<this.length;c++){var a=this[c][0];null!=a&&(s[a]=!0)}for(var l=0;l<t.length;l++){var d=[].concat(t[l]);i&&s[d[0]]||(void 0!==r&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=r),o&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=o):d[2]=o),n&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=n):d[4]="".concat(n)),e.push(d))}},e}},758:t=>{"use strict";t.exports=function(t){return t[1]}},591:t=>{"use strict";var e=[];function o(t){for(var o=-1,i=0;i<e.length;i++)if(e[i].identifier===t){o=i;break}return o}function i(t,i){for(var r={},s=[],c=0;c<t.length;c++){var a=t[c],l=i.base?a[0]+i.base:a[0],d=r[l]||0,u="".concat(l," ").concat(d);r[l]=d+1;var h=o(u),p={css:a[1],media:a[2],sourceMap:a[3],supports:a[4],layer:a[5]};if(-1!==h)e[h].references++,e[h].updater(p);else{var f=n(p,i);i.byIndex=c,e.splice(c,0,{identifier:u,updater:f,references:1})}s.push(u)}return s}function n(t,e){var o=e.domAPI(e);o.update(t);return function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;o.update(t=e)}else o.remove()}}t.exports=function(t,n){var r=i(t=t||[],n=n||{});return function(t){t=t||[];for(var s=0;s<r.length;s++){var c=o(r[s]);e[c].references--}for(var a=i(t,n),l=0;l<r.length;l++){var d=o(r[l]);0===e[d].references&&(e[d].updater(),e.splice(d,1))}r=a}}},747:t=>{"use strict";var e={};t.exports=function(t,o){var i=function(t){if(void 0===e[t]){var o=document.querySelector(t);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(t){o=null}e[t]=o}return e[t]}(t);if(!i)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");i.appendChild(o)}},51:t=>{"use strict";t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},21:t=>{"use strict";t.exports=function(t,e){Object.keys(e).forEach((function(o){t.setAttribute(o,e[o])}))}},639:t=>{"use strict";var e,o=(e=[],function(t,o){return e[t]=o,e.filter(Boolean).join("\n")});function i(t,e,i,n){var r;if(i)r="";else{r="",n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var s=void 0!==n.layer;s&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,s&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}")}if(t.styleSheet)t.styleSheet.cssText=o(e,r);else{var c=document.createTextNode(r),a=t.childNodes;a[e]&&t.removeChild(a[e]),a.length?t.insertBefore(c,a[e]):t.appendChild(c)}}var n={singleton:null,singletonCounter:0};t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=n.singletonCounter++,o=n.singleton||(n.singleton=t.insertStyleElement(t));return{update:function(t){i(o,e,!1,t)},remove:function(t){i(o,e,!0,t)}}}},782:(t,e,o)=>{t.exports=o(237)("./src/core.js")},783:(t,e,o)=>{t.exports=o(237)("./src/engine.js")},311:(t,e,o)=>{t.exports=o(237)("./src/ui.js")},584:(t,e,o)=>{t.exports=o(237)("./src/utils.js")},237:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function o(i){var n=e[i];if(void 0!==n)return n.exports;var r=e[i]={id:i,exports:{}};return t[i](r,r.exports,o),r.exports}o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var i in e)o.o(e,i)&&!o.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i={};(()=>{"use strict";o.r(i),o.d(i,{ClassicEditor:()=>G});var t=o(311),e=o(783),n=o(584);class r extends t.EditorUI{constructor(e,o){super(e),this.view=o,this._toolbarConfig=(0,t.normalizeToolbarConfig)(e.config.get("toolbar")),this._elementReplacer=new n.ElementReplacer,this.listenTo(e.editing.view,"scrollToTheSelection",this._handleScrollToTheSelectionWithStickyPanel.bind(this))}get element(){return this.view.element}init(t){const e=this.editor,o=this.view,i=e.editing.view,n=o.editable,r=i.document.getRoot();n.name=r.rootName,o.render();const s=n.element;this.setEditableElement(n.name,s),o.editable.bind("isFocused").to(this.focusTracker),i.attachDomRoot(s),t&&this._elementReplacer.replace(t,this.element),this._initPlaceholder(),this._initToolbar(),o.menuBarView&&this._initMenuBar(o.menuBarView),this._initDialogPluginIntegration(),this._initContextualBalloonIntegration(),this.fire("ready")}destroy(){super.destroy();const t=this.view,e=this.editor.editing.view;this._elementReplacer.restore(),e.getDomRoot(t.editable.name)&&e.detachDomRoot(t.editable.name),t.destroy()}_initToolbar(){const t=this.view;t.stickyPanel.bind("isActive").to(this.focusTracker,"isFocused"),t.stickyPanel.limiterElement=t.element,t.stickyPanel.bind("viewportTopOffset").to(this,"viewportOffset",(({top:t})=>t||0)),t.toolbar.fillFromConfig(this._toolbarConfig,this.componentFactory),this.addToolbar(t.toolbar)}_initPlaceholder(){const t=this.editor,o=t.editing.view,i=o.document.getRoot(),n=t.sourceElement;let r;const s=t.config.get("placeholder");s&&(r="string"==typeof s?s:s[this.view.editable.name]),!r&&n&&"textarea"===n.tagName.toLowerCase()&&(r=n.getAttribute("placeholder")),r&&(i.placeholder=r),(0,e.enablePlaceholder)({view:o,element:i,isDirectHost:!1,keepOnFocus:!0})}_initContextualBalloonIntegration(){if(!this.editor.plugins.has("ContextualBalloon"))return;const{stickyPanel:t}=this.view,e=this.editor.plugins.get("ContextualBalloon");e.on("getPositionOptions",(e=>{const o=e.return;if(!o||!t.isSticky||!t.element)return;const i=new n.Rect(t.element).height,r="function"==typeof o.target?o.target():o.target,s="function"==typeof o.limiter?o.limiter():o.limiter;if(r&&s&&new n.Rect(r).height>=new n.Rect(s).height-i)return;const c={...o.viewportOffsetConfig},a=(c.top||0)+i;e.return={...o,viewportOffsetConfig:{...c,top:a}}}),{priority:"low"});const o=()=>{e.visibleView&&e.updatePosition()};this.listenTo(t,"change:isSticky",o),this.listenTo(this.editor.ui,"change:viewportOffset",o)}_handleScrollToTheSelectionWithStickyPanel(t,e,o){const i=this.view.stickyPanel;if(i.isSticky){const t=new n.Rect(i.element).height;e.viewportOffset.top+=t}else{const t=()=>{this.editor.editing.view.scrollToTheSelection(o)};this.listenTo(i,"change:isSticky",t),setTimeout((()=>{this.stopListening(i,"change:isSticky",t)}),20)}}_initDialogPluginIntegration(){if(!this.editor.plugins.has("Dialog"))return;const e=this.view.stickyPanel,o=this.editor.plugins.get("Dialog");o.on("show",(()=>{const i=o.view;i.on("moveTo",((o,r)=>{if(!e.isSticky||i.wasMoved||i.isModal)return;const s=new n.Rect(e.contentPanelElement);r[1]<s.bottom+t.DialogView.defaultOffset&&(r[1]=s.bottom+t.DialogView.defaultOffset)}),{priority:"high"})}),{priority:"low"})}}var s=o(591),c=o.n(s),a=o(639),l=o.n(a),d=o(747),u=o.n(d),h=o(21),p=o.n(h),f=o(51),b=o.n(f),g=o(662),v={attributes:{"data-cke":!0}};v.setAttributes=p(),v.insert=u().bind(null,"head"),v.domAPI=l(),v.insertStyleElement=b();c()(g.A,v);g.A&&g.A.locals&&g.A.locals;class m extends t.BoxedEditorUIView{constructor(e,o,i={}){super(e),this.stickyPanel=new t.StickyPanelView(e),this.toolbar=new t.ToolbarView(e,{shouldGroupWhenFull:i.shouldToolbarGroupWhenFull}),i.useMenuBar&&(this.menuBarView=new t.MenuBarView(e)),this.editable=new t.InlineEditableUIView(e,o,void 0,{label:i.label})}render(){super.render(),this.menuBarView?this.stickyPanel.content.addMany([this.menuBarView,this.toolbar]):this.stickyPanel.content.add(this.toolbar),this.top.add(this.stickyPanel),this.main.add(this.editable)}}var k=o(782);const y=function(t){return null!=t&&"object"==typeof t};const w="object"==typeof global&&global&&global.Object===Object&&global;var _="object"==typeof self&&self&&self.Object===Object&&self;const T=(w||_||Function("return this")()).Symbol;var x=Object.prototype,S=x.hasOwnProperty,P=x.toString,O=T?T.toStringTag:void 0;const j=function(t){var e=S.call(t,O),o=t[O];try{t[O]=void 0;var i=!0}catch(t){}var n=P.call(t);return i&&(e?t[O]=o:delete t[O]),n};var E=Object.prototype.toString;const C=function(t){return E.call(t)};var B=T?T.toStringTag:void 0;const D=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":B&&B in Object(t)?j(t):C(t)};const M=function(t,e){return function(o){return t(e(o))}}(Object.getPrototypeOf,Object);var F=Function.prototype,R=Object.prototype,V=F.toString,A=R.hasOwnProperty,I=V.call(Object);const N=function(t){if(!y(t)||"[object Object]"!=D(t))return!1;var e=M(t);if(null===e)return!0;var o=A.call(e,"constructor")&&e.constructor;return"function"==typeof o&&o instanceof o&&V.call(o)==I};const W=function(t){return y(t)&&1===t.nodeType&&!N(t)};class G extends((0,k.ElementApiMixin)(k.Editor)){static get editorName(){return"ClassicEditor"}constructor(t,e={}){if(!K(t)&&void 0!==e.initialData)throw new n.CKEditorError("editor-create-initial-data",null);super(e),this.config.define("menuBar.isVisible",!1),void 0===this.config.get("initialData")&&this.config.set("initialData",function(t){return K(t)?(0,n.getDataFromElement)(t):t}(t)),K(t)&&(this.sourceElement=t),this.model.document.createRoot();const o=!this.config.get("toolbar.shouldNotGroupWhenFull"),i=this.config.get("menuBar"),s=new m(this.locale,this.editing.view,{shouldToolbarGroupWhenFull:o,useMenuBar:i.isVisible,label:this.config.get("label")});this.ui=new r(this,s),(0,k.attachToForm)(this)}destroy(){return this.sourceElement&&this.updateSourceElement(),this.ui.destroy(),super.destroy()}static create(t,e={}){return new Promise((o=>{const i=new this(t,e);o(i.initPlugins().then((()=>i.ui.init(K(t)?t:null))).then((()=>i.data.init(i.config.get("initialData")))).then((()=>i.fire("ready"))).then((()=>i)))}))}}function K(t){return W(t)}})(),(window.CKEditor5=window.CKEditor5||{}).editorClassic=i})();
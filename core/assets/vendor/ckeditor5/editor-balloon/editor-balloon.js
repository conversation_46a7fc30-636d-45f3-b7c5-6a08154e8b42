/*!
 * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={782:(t,e,o)=>{t.exports=o(237)("./src/core.js")},783:(t,e,o)=>{t.exports=o(237)("./src/engine.js")},311:(t,e,o)=>{t.exports=o(237)("./src/ui.js")},584:(t,e,o)=>{t.exports=o(237)("./src/utils.js")},237:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function o(i){var r=e[i];if(void 0!==r)return r.exports;var n=e[i]={exports:{}};return t[i](n,n.exports,o),n.exports}o.d=(t,e)=>{for(var i in e)o.o(e,i)&&!o.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i={};(()=>{"use strict";o.r(i),o.d(i,{BalloonEditor:()=>B});var t=o(782),e=o(311),r=o(584),n=o(783);class s extends e.EditorUI{constructor(t,e){super(t),this.view=e}get element(){return this.view.editable.element}init(){const t=this.editor,e=this.view,o=t.editing.view,i=e.editable,r=o.document.getRoot();i.name=r.rootName,e.render();const n=i.element;this.setEditableElement(i.name,n),i.bind("isFocused").to(this.focusTracker),o.attachDomRoot(n),this._initPlaceholder(),this._initMenuBar(this.view.menuBarView),this.fire("ready")}destroy(){super.destroy();const t=this.view,e=this.editor.editing.view;e.getDomRoot(t.editable.name)&&e.detachDomRoot(t.editable.name),t.destroy()}_initPlaceholder(){const t=this.editor,e=t.editing.view,o=e.document.getRoot(),i=t.config.get("placeholder");if(i){const t="string"==typeof i?i:i[o.rootName];t&&(o.placeholder=t)}(0,n.enablePlaceholder)({view:e,element:o,isDirectHost:!1,keepOnFocus:!0})}}class c extends e.EditorUIView{constructor(t,o,i,r){super(t),this.editable=new e.InlineEditableUIView(t,o,i,{label:r}),this.menuBarView=new e.MenuBarView(t),this.menuBarView.extendTemplate({attributes:{class:["ck-reset_all","ck-rounded-corners"],dir:t.uiLanguageDirection}})}render(){super.render(),this.registerChild(this.editable),this.registerChild(this.menuBarView)}}const a=function(t){return null!=t&&"object"==typeof t};const l="object"==typeof global&&global&&global.Object===Object&&global;var u="object"==typeof self&&self&&self.Object===Object&&self;const d=(l||u||Function("return this")()).Symbol;var h=Object.prototype,g=h.hasOwnProperty,f=h.toString,b=d?d.toStringTag:void 0;const p=function(t){var e=g.call(t,b),o=t[b];try{t[b]=void 0;var i=!0}catch(t){}var r=f.call(t);return i&&(e?t[b]=o:delete t[b]),r};var m=Object.prototype.toString;const v=function(t){return m.call(t)};var w=d?d.toStringTag:void 0;const y=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":w&&w in Object(t)?p(t):v(t)};const j=function(t,e){return function(o){return t(e(o))}}(Object.getPrototypeOf,Object);var E=Function.prototype,O=Object.prototype,x=E.toString,S=O.hasOwnProperty,P=x.call(Object);const T=function(t){if(!a(t)||"[object Object]"!=y(t))return!1;var e=j(t);if(null===e)return!0;var o=S.call(e,"constructor")&&e.constructor;return"function"==typeof o&&o instanceof o&&x.call(o)==P};const D=function(t){return a(t)&&1===t.nodeType&&!T(t)};class B extends((0,t.ElementApiMixin)(t.Editor)){static get editorName(){return"BalloonEditor"}constructor(o,i={}){if(!C(o)&&void 0!==i.initialData)throw new r.CKEditorError("editor-create-initial-data",null);super(i),void 0===this.config.get("initialData")&&this.config.set("initialData",function(t){return C(t)?(0,r.getDataFromElement)(t):t}(o)),C(o)&&(this.sourceElement=o,(0,t.secureSourceElement)(this,o));const n=this.config.get("plugins");n.push(e.BalloonToolbar),this.config.set("plugins",n),this.config.define("balloonToolbar",this.config.get("toolbar")),this.model.document.createRoot();const a=new c(this.locale,this.editing.view,this.sourceElement,this.config.get("label"));this.ui=new s(this,a),(0,t.attachToForm)(this)}destroy(){const t=this.getData();return this.ui.destroy(),super.destroy().then((()=>{this.sourceElement&&this.updateSourceElement(t)}))}static create(t,e={}){return new Promise((o=>{if(C(t)&&"TEXTAREA"===t.tagName)throw new r.CKEditorError("editor-wrong-element",null);const i=new this(t,e);o(i.initPlugins().then((()=>i.ui.init())).then((()=>i.data.init(i.config.get("initialData")))).then((()=>i.fire("ready"))).then((()=>i)))}))}}function C(t){return D(t)}})(),(window.CKEditor5=window.CKEditor5||{}).editorBalloon=i})();
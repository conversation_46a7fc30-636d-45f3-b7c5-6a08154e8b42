{"$id": "https://git.drupalcode.org/project/drupal/-/raw/HEAD/core/assets/schemas/v1/metadata.schema.json", "$schema": "http://json-schema.org/draft-04/schema#", "$defs": {"slotDefinition": {"type": "object", "patternProperties": {"^[a-zA-Z0-9_-]+$": {"type": "object", "properties": {"title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "examples": {"type": "array", "items": {"type": "string"}}}}}}, "cssAttributes": {"type": ["object", "array"], "patternProperties": {".*": {"type": ["object", "array"], "properties": {"attributes": {"type": "object"}, "group": {"type": "string"}, "media": {"type": "string"}, "minified": {"type": "boolean"}, "preprocess": {"type": "boolean"}, "type": {"type": "string"}, "weight": {"type": "integer"}}}}}, "cssDefinition": {"type": ["object", "array"], "properties": {"base": {"$ref": "#/$defs/cssAttributes"}, "layout": {"$ref": "#/$defs/cssAttributes"}, "component": {"$ref": "#/$defs/cssAttributes"}, "state": {"$ref": "#/$defs/cssAttributes"}, "theme": {"$ref": "#/$defs/cssAttributes"}}}, "jsAttributes": {"type": ["object", "array"], "patternProperties": {".*": {"type": ["object", "array"], "properties": {"attributes": {"type": "object"}, "preprocess": {"type": "boolean"}, "type": {"type": "string"}, "weight": {"type": "number"}}}}}, "libraryDefinition": {"type": "object", "properties": {"dependencies": {"type": "array", "items": {"type": "string"}}, "css": {"type": ["object", "array"], "properties": {"base": {"$ref": "#/$defs/cssAttributes"}, "layout": {"$ref": "#/$defs/cssAttributes"}, "component": {"$ref": "#/$defs/cssAttributes"}, "state": {"$ref": "#/$defs/cssAttributes"}, "theme": {"$ref": "#/$defs/cssAttributes"}}}, "js": {"$ref": "#/$defs/jsAttributes"}}}}, "type": "object", "properties": {"$schema": {"type": "string", "format": "uri"}, "name": {"type": "string", "title": "Name", "description": "Human readable version of the component name", "examples": ["<PERSON><PERSON>", "Module 23F"]}, "description": {"type": "string", "title": "Description", "description": "Human readable description for the component", "examples": ["Plain button. Only available for use in other components. Do not include directly."]}, "status": {"type": "string", "title": "Development Status", "description": "Use this property to identify if the component can be safely used in production.", "enum": ["experimental", "stable", "deprecated", "obsolete"]}, "props": {"$ref": "http://json-schema.org/draft-04/schema#"}, "slots": {"$ref": "#/$defs/slotDefinition"}, "libraryOverrides": {"$ref": "#/$defs/libraryDefinition"}, "thirdPartySettings": {"type": ["object", "array"], "additionalProperties": true, "patternProperties": {".*": {"type": "object", "additionalProperties": true}}}}}
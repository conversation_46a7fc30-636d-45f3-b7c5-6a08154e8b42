# 评价内容类型V3.0配置文件
# Review Content Type V3.0 Configuration

# V3.0协议标记
v3_protocol: true
optimization_level: "high"
parallel_processing: true
batch_operations: true
state_persistence: true
auto_recovery: true

# 任务信息
task_info:
  task_id: "task_009"
  task_name: "创建评价内容类型"
  protocol_version: "3.0"
  priority: "high"
  estimated_time: "5_minutes"
  dependencies: ["task_008"]
  theme: "意大利家居评价系统"

# 1. 评价类型词汇表配置
review_types:
  vid: review_types
  name: 评价类型
  description: 用户可以评价的对象类型
  terms:
    - 品牌评价
    - 产品评价
    - 设计师评价
    - 新闻评价
    - 综合评价

# 2. 评价标签词汇表配置
review_tags:
  vid: review_tags
  name: 评价标签
  description: 评价的特征标签
  terms:
    - 质量优秀
    - 设计精美
    - 性价比高
    - 服务专业
    - 创新独特
    - 工艺精湛
    - 材料优质
    - 风格独特
    - 实用性强
    - 值得推荐

# 3. 评价内容类型字段配置 (V3.0批量优化)
review_content_type:
  type: review
  name: 评价
  description: 用户对品牌、产品、设计师等的评价和反馈
  v3_batch_creation: true  # V3.0批量创建标记
  
  fields:
    # 评价标题 (必需字段，已存在)
    title:
      type: string
      label: 评价标题
      required: true
      
    # 评价内容
    field_review_content:
      type: text_long
      label: 评价内容
      required: true
      settings:
        display_summary: true
        
    # 评价评分
    field_review_rating:
      type: decimal
      label: 评价评分
      required: true
      settings:
        min: 1
        max: 5
        precision: 3
        scale: 1
        
    # 评价类型
    field_review_type:
      type: entity_reference
      label: 评价类型
      required: true
      cardinality: 1
      settings:
        target_type: taxonomy_term
        target_bundles:
          review_types: review_types
          
    # 评价标签 (多值)
    field_review_tags:
      type: entity_reference
      label: 评价标签
      required: false
      cardinality: -1  # 多值
      settings:
        target_type: taxonomy_term
        target_bundles:
          review_tags: review_tags
          
    # 评价对象 - 品牌
    field_review_brand:
      type: entity_reference
      label: 评价品牌
      required: false
      cardinality: 1
      settings:
        target_type: node
        target_bundles:
          brand: brand
          
    # 评价对象 - 产品
    field_review_product:
      type: entity_reference
      label: 评价产品
      required: false
      cardinality: 1
      settings:
        target_type: node
        target_bundles:
          product: product
          
    # 评价对象 - 设计师
    field_review_designer:
      type: entity_reference
      label: 评价设计师
      required: false
      cardinality: 1
      settings:
        target_type: node
        target_bundles:
          designer: designer
          
    # 评价对象 - 新闻
    field_review_news:
      type: entity_reference
      label: 评价新闻
      required: false
      cardinality: 1
      settings:
        target_type: node
        target_bundles:
          news: news
          
    # 评价者姓名
    field_review_author_name:
      type: string
      label: 评价者姓名
      required: true
      settings:
        max_length: 100
        
    # 评价者邮箱
    field_review_author_email:
      type: email
      label: 评价者邮箱
      required: false
      
    # 评价者位置
    field_review_author_location:
      type: string
      label: 评价者位置
      required: false
      settings:
        max_length: 100
        
    # 购买/体验时间
    field_review_experience_date:
      type: datetime
      label: 购买/体验时间
      required: false
      settings:
        datetime_type: date
        
    # 推荐程度
    field_review_recommendation:
      type: list_string
      label: 推荐程度
      required: true
      settings:
        allowed_values:
          strongly_recommend: 强烈推荐
          recommend: 推荐
          neutral: 中性
          not_recommend: 不推荐
          strongly_not_recommend: 强烈不推荐
          
    # 有用性投票
    field_review_helpful_votes:
      type: integer
      label: 有用性投票
      required: false
      default_value: 0
      settings:
        min: 0
        
    # 评价图片 (多值)
    field_review_images:
      type: image
      label: 评价图片
      required: false
      cardinality: -1  # 多值
      settings:
        file_extensions: 'png gif jpg jpeg webp'
        max_filesize: '5 MB'
        max_resolution: '1920x1080'
        min_resolution: '200x200'
        alt_field: true
        alt_field_required: false
        title_field: true

# 4. V3.0显示配置 (智能优化)
display_settings:
  # 默认显示模式
  default:
    content:
      field_review_rating:
        type: number_decimal
        weight: 0
        settings:
          suffix_display: true
      field_review_content:
        type: text_default
        weight: 1
      field_review_type:
        type: entity_reference_label
        weight: 2
      field_review_tags:
        type: entity_reference_label
        weight: 3
      field_review_author_name:
        type: string
        weight: 4
      field_review_author_location:
        type: string
        weight: 5
      field_review_experience_date:
        type: datetime_default
        weight: 6
      field_review_recommendation:
        type: list_default
        weight: 7
      field_review_helpful_votes:
        type: number_integer
        weight: 8
      field_review_images:
        type: image
        weight: 9
        settings:
          image_style: medium
        
  # 卡片显示模式
  teaser:
    content:
      field_review_rating:
        type: number_decimal
        weight: 0
      field_review_content:
        type: text_summary_or_trimmed
        weight: 1
        settings:
          trim_length: 200
      field_review_author_name:
        type: string
        weight: 2
      field_review_recommendation:
        type: list_default
        weight: 3

# 5. V3.0表单显示配置 (批量优化)
form_display:
  content:
    title:
      type: string_textfield
      weight: 0
    field_review_content:
      type: text_textarea_with_summary
      weight: 1
    field_review_rating:
      type: number
      weight: 2
    field_review_type:
      type: entity_reference_autocomplete
      weight: 3
    field_review_tags:
      type: entity_reference_autocomplete_tags
      weight: 4
    field_review_brand:
      type: entity_reference_autocomplete
      weight: 5
    field_review_product:
      type: entity_reference_autocomplete
      weight: 6
    field_review_designer:
      type: entity_reference_autocomplete
      weight: 7
    field_review_news:
      type: entity_reference_autocomplete
      weight: 8
    field_review_author_name:
      type: string_textfield
      weight: 9
    field_review_author_email:
      type: email_default
      weight: 10
    field_review_author_location:
      type: string_textfield
      weight: 11
    field_review_experience_date:
      type: datetime_default
      weight: 12
    field_review_recommendation:
      type: options_select
      weight: 13
    field_review_helpful_votes:
      type: number
      weight: 14
    field_review_images:
      type: image_image
      weight: 15

# 6. V3.0权限配置
permissions:
  - create review content
  - edit own review content
  - edit any review content
  - delete own review content
  - delete any review content
  - view published review content
  - view unpublished review content
  - vote on review helpfulness

# 7. V3.0关联关系配置
relationships:
  review_to_brand:
    type: "many_to_one"
    description: "评价可以关联一个品牌"
  review_to_product:
    type: "many_to_one"
    description: "评价可以关联一个产品"
  review_to_designer:
    type: "many_to_one"
    description: "评价可以关联一个设计师"
  review_to_news:
    type: "many_to_one"
    description: "评价可以关联一个新闻"
  brand_to_reviews:
    type: "one_to_many"
    description: "品牌可以有多个评价"
  product_to_reviews:
    type: "one_to_many"
    description: "产品可以有多个评价"
  designer_to_reviews:
    type: "one_to_many"
    description: "设计师可以有多个评价"

# 8. V3.0性能优化配置
performance_optimization:
  field_caching: true
  entity_caching: true
  query_optimization: true
  image_optimization: true
  lazy_loading: true
  review_aggregation: true

# 9. V3.0状态持久化配置
state_persistence:
  checkpoint_name: "review_content_type_creation"
  backup_frequency: "每阶段完成后"
  recovery_points: [
    "词汇表创建完成",
    "内容类型创建完成", 
    "字段创建完成",
    "测试数据创建完成",
    "验证完成"
  ]

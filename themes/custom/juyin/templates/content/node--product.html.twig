{#
/**
 * @file
 * Theme implementation to display a product node.
 */
#}

<article{{ attributes.addClass('product-card', 'bg-white', 'rounded-lg', 'shadow-soft', 'overflow-hidden', 'hover:shadow-medium', 'transition-shadow', 'duration-300', 'group') }}>
  
  {# Product Image #}
  {% if content.field_product_images %}
    <div class="product-image aspect-w-1 aspect-h-1 bg-gray-100 relative overflow-hidden">
      {{ content.field_product_images }}
      
      {# Quick Action Overlay #}
      <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
        <div class="flex space-x-2">
          <button class="bg-white p-2 rounded-full shadow-md hover:shadow-lg transition-shadow duration-200">
            <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
          </button>
          <button class="bg-white p-2 rounded-full shadow-md hover:shadow-lg transition-shadow duration-200">
            <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  {% endif %}

  <div class="p-4">
    {# Product Brand #}
    {% if content.field_product_brand %}
      <div class="product-brand text-xs text-gray-500 uppercase tracking-wide mb-1">
        {{ content.field_product_brand }}
      </div>
    {% endif %}

    {# Product Name #}
    <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
      <a href="{{ url }}" class="hover:text-primary-600 transition-colors duration-200">
        {{ label }}
      </a>
    </h3>

    {# Product Category #}
    {% if content.field_product_category %}
      <div class="product-category mb-2">
        <span class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
          {{ content.field_product_category }}
        </span>
      </div>
    {% endif %}

    {# Product Description #}
    {% if content.field_product_description %}
      <div class="product-description text-gray-600 text-sm mb-3 line-clamp-2">
        {{ content.field_product_description }}
      </div>
    {% endif %}

    {# Product Rating #}
    {% if content.field_product_rating %}
      <div class="product-rating flex items-center mb-3">
        <div class="flex items-center">
          {% for i in 1..5 %}
            <svg class="w-3 h-3 {{ i <= content.field_product_rating['#items'].0.value ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          {% endfor %}
        </div>
        <span class="ml-1 text-xs text-gray-500">
          ({{ content.field_product_rating['#items'].0.value }})
        </span>
      </div>
    {% endif %}

    {# Product Price #}
    {% if content.field_product_price %}
      <div class="product-price mb-4">
        <span class="text-lg font-bold text-primary-600">
          ¥{{ content.field_product_price['#items'].0.value }}
        </span>
        {% if content.field_original_price %}
          <span class="text-sm text-gray-500 line-through ml-2">
            ¥{{ content.field_original_price['#items'].0.value }}
          </span>
        {% endif %}
      </div>
    {% endif %}

    {# Action Buttons #}
    <div class="flex space-x-2">
      <a href="{{ url }}" class="flex-1 bg-primary-600 text-white text-center py-2 px-3 rounded-md hover:bg-primary-700 transition-colors duration-200 text-sm font-medium">
        查看详情
      </a>
      <button class="compare-btn p-2 border border-gray-300 rounded-md hover:border-primary-600 hover:text-primary-600 transition-colors duration-200">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
        </svg>
      </button>
    </div>
  </div>

</article>

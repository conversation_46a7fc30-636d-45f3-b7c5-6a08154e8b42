{#
/**
 * @file
 * Theme implementation to display a brand node.
 */
#}

<article{{ attributes.addClass('brand-card', 'bg-white', 'rounded-lg', 'shadow-soft', 'overflow-hidden', 'hover:shadow-medium', 'transition-shadow', 'duration-300') }}>
  
  {# Brand Logo #}
  {% if content.field_brand_logo %}
    <div class="brand-logo aspect-w-16 aspect-h-9 bg-gray-100">
      {{ content.field_brand_logo }}
    </div>
  {% endif %}

  <div class="p-6">
    {# Brand Name #}
    <h2 class="text-xl font-semibold text-gray-900 mb-2">
      <a href="{{ url }}" class="hover:text-primary-600 transition-colors duration-200">
        {{ label }}
      </a>
    </h2>

    {# Brand Category #}
    {% if content.field_brand_category %}
      <div class="brand-category mb-3">
        {{ content.field_brand_category }}
      </div>
    {% endif %}

    {# Brand Description #}
    {% if content.field_brand_description %}
      <div class="brand-description text-gray-600 text-sm mb-4 line-clamp-3">
        {{ content.field_brand_description }}
      </div>
    {% endif %}

    {# Brand Rating #}
    {% if content.field_brand_rating %}
      <div class="brand-rating flex items-center mb-4">
        <div class="flex items-center">
          {% for i in 1..5 %}
            <svg class="w-4 h-4 {{ i <= content.field_brand_rating['#items'].0.value ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          {% endfor %}
        </div>
        <span class="ml-2 text-sm text-gray-600">
          ({{ content.field_brand_rating['#items'].0.value }})
        </span>
      </div>
    {% endif %}

    {# Product Count #}
    {% if content.field_product_count %}
      <div class="product-count text-sm text-gray-500 mb-4">
        {{ content.field_product_count['#items'].0.value }} 个产品
      </div>
    {% endif %}

    {# Action Buttons #}
    <div class="flex space-x-3">
      <a href="{{ url }}" class="flex-1 bg-primary-600 text-white text-center py-2 px-4 rounded-md hover:bg-primary-700 transition-colors duration-200 text-sm font-medium">
        查看详情
      </a>
      <button class="favorite-btn p-2 border border-gray-300 rounded-md hover:border-primary-600 hover:text-primary-600 transition-colors duration-200">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
        </svg>
      </button>
    </div>
  </div>

</article>

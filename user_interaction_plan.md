# 用户交互功能规划

## 1. 收藏功能

### 收藏按钮组件
```javascript
// 收藏功能 JavaScript
class FavoriteManager {
  constructor() {
    this.init();
  }

  init() {
    document.addEventListener('click', (e) => {
      if (e.target.closest('.favorite-btn')) {
        this.toggleFavorite(e.target.closest('.favorite-btn'));
      }
    });
  }

  async toggleFavorite(button) {
    const entityId = button.dataset.entityId;
    const entityType = button.dataset.entityType;
    const isFavorited = button.classList.contains('favorited');

    try {
      const response = await fetch('/api/favorites/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': drupalSettings.csrf_token
        },
        body: JSON.stringify({
          entity_id: entityId,
          entity_type: entityType,
          action: isFavorited ? 'remove' : 'add'
        })
      });

      if (response.ok) {
        this.updateFavoriteButton(button, !isFavorited);
        this.showNotification(isFavorited ? '已取消收藏' : '已添加到收藏');
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
      this.showNotification('操作失败，请重试', 'error');
    }
  }

  updateFavoriteButton(button, isFavorited) {
    if (isFavorited) {
      button.classList.add('favorited', 'text-red-500', 'border-red-500');
      button.querySelector('svg').setAttribute('fill', 'currentColor');
    } else {
      button.classList.remove('favorited', 'text-red-500', 'border-red-500');
      button.querySelector('svg').setAttribute('fill', 'none');
    }
  }

  showNotification(message, type = 'success') {
    // 显示通知消息
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white ${
      type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  }
}

// 初始化收藏管理器
new FavoriteManager();
```

### 收藏列表页面
```html
<div class="favorites-page">
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">我的收藏</h1>
    
    <!-- 收藏分类标签 -->
    <div class="mb-6">
      <nav class="flex space-x-8">
        <a href="#products" class="tab-link active border-b-2 border-primary-500 pb-2 text-primary-600 font-medium">
          产品 (23)
        </a>
        <a href="#brands" class="tab-link border-b-2 border-transparent pb-2 text-gray-500 hover:text-gray-700">
          品牌 (8)
        </a>
        <a href="#designers" class="tab-link border-b-2 border-transparent pb-2 text-gray-500 hover:text-gray-700">
          设计师 (5)
        </a>
        <a href="#articles" class="tab-link border-b-2 border-transparent pb-2 text-gray-500 hover:text-gray-700">
          文章 (12)
        </a>
      </nav>
    </div>

    <!-- 收藏内容 -->
    <div class="tab-content">
      <div id="products" class="tab-pane active">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <!-- 产品卡片 -->
        </div>
      </div>
      <!-- 其他标签页内容 -->
    </div>
  </div>
</div>
```

## 2. 评价和评论系统

### 评价表单
```html
<div class="review-form bg-white rounded-lg shadow-soft p-6">
  <h3 class="text-lg font-semibold mb-4">发表评价</h3>
  
  <form id="review-form">
    <!-- 评分 -->
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">总体评分</label>
      <div class="flex items-center space-x-1">
        <div class="rating-stars flex">
          {% for i in 1..5 %}
            <button type="button" class="star-btn text-gray-300 hover:text-yellow-400 focus:text-yellow-400" data-rating="{{ i }}">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
            </button>
          {% endfor %}
        </div>
        <span class="ml-2 text-sm text-gray-600">请选择评分</span>
      </div>
    </div>

    <!-- 评价标题 -->
    <div class="mb-4">
      <label for="review-title" class="block text-sm font-medium text-gray-700 mb-2">评价标题</label>
      <input type="text" id="review-title" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500" placeholder="简要描述您的使用感受">
    </div>

    <!-- 评价内容 -->
    <div class="mb-4">
      <label for="review-content" class="block text-sm font-medium text-gray-700 mb-2">详细评价</label>
      <textarea id="review-content" rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500" placeholder="分享您的详细使用体验..."></textarea>
    </div>

    <!-- 图片上传 -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">上传图片 (可选)</label>
      <div class="flex items-center space-x-4">
        <div class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-primary-500">
          <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          <p class="mt-1 text-sm text-gray-600">点击上传图片</p>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="flex justify-end">
      <button type="submit" class="bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700 transition-colors duration-200">
        发布评价
      </button>
    </div>
  </form>
</div>
```

### 评价列表展示
```html
<div class="reviews-list">
  <div class="reviews-header flex justify-between items-center mb-6">
    <h3 class="text-lg font-semibold">用户评价 (156)</h3>
    <div class="flex items-center space-x-4">
      <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
        <option value="newest">最新</option>
        <option value="oldest">最早</option>
        <option value="highest">评分最高</option>
        <option value="lowest">评分最低</option>
      </select>
    </div>
  </div>

  <div class="space-y-6">
    <div class="review-item bg-gray-50 rounded-lg p-4">
      <div class="flex items-start space-x-4">
        <img src="/path/to/avatar.jpg" alt="用户头像" class="w-10 h-10 rounded-full">
        <div class="flex-1">
          <div class="flex items-center justify-between mb-2">
            <div>
              <h4 class="font-medium text-gray-900">张三</h4>
              <div class="flex items-center mt-1">
                <div class="flex text-yellow-400">
                  <!-- 5星评分 -->
                </div>
                <span class="ml-2 text-sm text-gray-600">2024-01-15</span>
              </div>
            </div>
          </div>
          
          <h5 class="font-medium text-gray-900 mb-2">质量很好，值得推荐</h5>
          <p class="text-gray-700 mb-3">
            产品质量确实不错，做工精细，材质也很好。客服态度也很好，物流很快。总体来说很满意，会推荐给朋友。
          </p>
          
          <!-- 评价图片 -->
          <div class="flex space-x-2 mb-3">
            <img src="/path/to/review-image1.jpg" alt="评价图片" class="w-16 h-16 rounded-md object-cover cursor-pointer">
            <img src="/path/to/review-image2.jpg" alt="评价图片" class="w-16 h-16 rounded-md object-cover cursor-pointer">
          </div>
          
          <!-- 有用按钮 -->
          <div class="flex items-center space-x-4 text-sm">
            <button class="flex items-center text-gray-500 hover:text-primary-600">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"/>
              </svg>
              有用 (23)
            </button>
            <button class="text-gray-500 hover:text-primary-600">回复</button>
          </div>
        </div>
      </div>
    </div>
    <!-- 更多评价项目 -->
  </div>
</div>
```

## 3. 产品对比功能

### 对比按钮和管理
```javascript
class ProductCompare {
  constructor() {
    this.compareList = JSON.parse(localStorage.getItem('compareList') || '[]');
    this.maxCompareItems = 4;
    this.init();
  }

  init() {
    this.updateCompareCounter();
    this.bindEvents();
  }

  bindEvents() {
    document.addEventListener('click', (e) => {
      if (e.target.closest('.compare-btn')) {
        this.toggleCompare(e.target.closest('.compare-btn'));
      }
      if (e.target.closest('.compare-bar-close')) {
        this.removeFromCompare(e.target.closest('.compare-item').dataset.productId);
      }
    });
  }

  toggleCompare(button) {
    const productId = button.dataset.productId;
    const productName = button.dataset.productName;
    const productImage = button.dataset.productImage;

    if (this.compareList.find(item => item.id === productId)) {
      this.removeFromCompare(productId);
    } else {
      this.addToCompare({
        id: productId,
        name: productName,
        image: productImage
      });
    }
  }

  addToCompare(product) {
    if (this.compareList.length >= this.maxCompareItems) {
      this.showNotification('最多只能对比4个产品', 'warning');
      return;
    }

    this.compareList.push(product);
    this.saveCompareList();
    this.updateUI();
    this.showNotification('已添加到对比列表');
  }

  removeFromCompare(productId) {
    this.compareList = this.compareList.filter(item => item.id !== productId);
    this.saveCompareList();
    this.updateUI();
  }

  updateUI() {
    this.updateCompareCounter();
    this.updateCompareBar();
    this.updateCompareButtons();
  }

  updateCompareCounter() {
    const counter = document.querySelector('.compare-counter');
    if (counter) {
      counter.textContent = this.compareList.length;
      counter.style.display = this.compareList.length > 0 ? 'block' : 'none';
    }
  }

  saveCompareList() {
    localStorage.setItem('compareList', JSON.stringify(this.compareList));
  }
}

// 初始化产品对比功能
new ProductCompare();
```

### 对比栏组件
```html
<div class="compare-bar fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg z-40 transform translate-y-full transition-transform duration-300" id="compare-bar">
  <div class="container mx-auto px-4 py-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <span class="text-sm font-medium text-gray-700">对比产品 (<span class="compare-count">0</span>/4)</span>
        <div class="flex space-x-2" id="compare-items">
          <!-- 对比产品项目 -->
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <button class="text-sm text-gray-500 hover:text-gray-700" onclick="clearCompareList()">清空</button>
        <button class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm hover:bg-primary-700 disabled:opacity-50" id="compare-btn" disabled>
          开始对比
        </button>
      </div>
    </div>
  </div>
</div>
```

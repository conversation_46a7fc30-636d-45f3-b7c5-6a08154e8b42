# 品牌内容类型配置文件
# Brand Content Type Configuration

# 1. 品牌分类词汇表配置
brand_categories:
  vid: brand_categories
  name: 品牌分类
  description: 用于分类不同类型的品牌
  terms:
    - 家具品牌
    - 装饰品牌
    - 照明品牌
    - 纺织品牌
    - 厨卫品牌
    - 智能家居品牌

# 2. 品牌内容类型字段配置
brand_content_type:
  type: brand
  name: 品牌
  description: 家居品牌信息管理
  
  fields:
    # 品牌名称 (必需字段，已存在)
    title:
      type: string
      label: 品牌名称
      required: true
      
    # 品牌Logo
    field_brand_logo:
      type: image
      label: 品牌Logo
      required: false
      settings:
        file_extensions: 'png gif jpg jpeg'
        max_filesize: '2 MB'
        max_resolution: '1200x800'
        min_resolution: '100x100'
        
    # 品牌描述
    field_brand_description:
      type: text_long
      label: 品牌描述
      required: false
      settings:
        display_summary: true
        
    # 品牌分类
    field_brand_category:
      type: entity_reference
      label: 品牌分类
      required: true
      settings:
        target_type: taxonomy_term
        target_bundles:
          brand_categories: brand_categories
        
    # 联系信息
    field_brand_contact:
      type: string
      label: 联系信息
      required: false
      settings:
        max_length: 255
        
    # 官网链接
    field_brand_website:
      type: link
      label: 官网链接
      required: false
      settings:
        link_type: 16  # External links only
        title: 1       # Optional title
        
    # 品牌评分
    field_brand_rating:
      type: decimal
      label: 品牌评分
      required: false
      settings:
        min: 0
        max: 5
        precision: 2
        scale: 1

# 3. 显示配置
display_settings:
  # 默认显示模式
  default:
    content:
      field_brand_logo:
        type: image
        weight: 0
        settings:
          image_style: medium
      field_brand_description:
        type: text_default
        weight: 1
      field_brand_category:
        type: entity_reference_label
        weight: 2
      field_brand_contact:
        type: string
        weight: 3
      field_brand_website:
        type: link
        weight: 4
      field_brand_rating:
        type: number_decimal
        weight: 5
        
  # 预览显示模式
  teaser:
    content:
      field_brand_logo:
        type: image
        weight: 0
        settings:
          image_style: thumbnail
      field_brand_category:
        type: entity_reference_label
        weight: 1
      field_brand_rating:
        type: number_decimal
        weight: 2

# 4. 表单显示配置
form_display:
  content:
    title:
      type: string_textfield
      weight: 0
    field_brand_logo:
      type: image_image
      weight: 1
    field_brand_description:
      type: text_textarea_with_summary
      weight: 2
    field_brand_category:
      type: options_select
      weight: 3
    field_brand_contact:
      type: string_textfield
      weight: 4
    field_brand_website:
      type: link_default
      weight: 5
    field_brand_rating:
      type: number
      weight: 6

# 5. 权限配置
permissions:
  - create brand content
  - edit own brand content
  - edit any brand content
  - delete own brand content
  - delete any brand content
  - view published brand content
  - view unpublished brand content

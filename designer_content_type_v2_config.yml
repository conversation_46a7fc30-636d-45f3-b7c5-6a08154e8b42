# 设计师内容类型V2.0配置文件
# Designer Content Type V2.0 Configuration

# V2.0协议优化标记
v2_protocol: true
optimization_level: "high"
parallel_processing: true
batch_operations: true

# 1. 设计师专业领域词汇表配置
designer_specialties:
  vid: designer_specialties
  name: 设计师专业领域
  description: 设计师的专业分工和擅长领域
  terms:
    - 室内设计
    - 软装设计
    - 家具设计
    - 灯光设计
    - 空间规划
    - 色彩搭配
    - 定制设计
    - 商业空间设计
    - 住宅设计
    - 概念设计

# 2. 设计风格词汇表配置
design_styles:
  vid: design_styles
  name: 设计风格
  description: 设计师擅长的设计风格
  terms:
    - 现代简约
    - 北欧风格
    - 中式传统
    - 欧式古典
    - 美式乡村
    - 工业风格
    - 地中海风格
    - 日式禅意
    - 轻奢风格
    - 混搭风格

# 3. 设计师内容类型字段配置 (V2.0批量优化)
designer_content_type:
  type: designer
  name: 设计师
  description: 家居设计师信息管理
  v2_batch_creation: true  # V2.0批量创建标记
  
  fields:
    # 设计师姓名 (必需字段，已存在)
    title:
      type: string
      label: 设计师姓名
      required: true
      
    # 设计师头像
    field_designer_avatar:
      type: image
      label: 设计师头像
      required: true
      cardinality: 1
      settings:
        file_extensions: 'png gif jpg jpeg webp'
        max_filesize: '2 MB'
        max_resolution: '800x800'
        min_resolution: '200x200'
        alt_field: true
        alt_field_required: true
        
    # 设计师简介
    field_designer_bio:
      type: text_long
      label: 设计师简介
      required: true
      settings:
        display_summary: true
        
    # 专业领域 (多值)
    field_designer_specialties:
      type: entity_reference
      label: 专业领域
      required: true
      cardinality: -1  # 多值
      settings:
        target_type: taxonomy_term
        target_bundles:
          designer_specialties: designer_specialties
          
    # 擅长风格 (多值)
    field_designer_styles:
      type: entity_reference
      label: 擅长风格
      required: false
      cardinality: -1  # 多值
      settings:
        target_type: taxonomy_term
        target_bundles:
          design_styles: design_styles
          
    # 从业年限
    field_designer_experience:
      type: integer
      label: 从业年限
      required: true
      settings:
        min: 0
        max: 50
        suffix: '年'
        
    # 联系方式
    field_designer_contact:
      type: string
      label: 联系方式
      required: false
      settings:
        max_length: 255
        
    # 个人网站
    field_designer_website:
      type: link
      label: 个人网站
      required: false
      settings:
        link_type: 16  # External links only
        title: 1       # Optional title
        
    # 作品集 (多值图片)
    field_designer_portfolio:
      type: image
      label: 作品集
      required: false
      cardinality: -1  # 无限制多值
      settings:
        file_extensions: 'png gif jpg jpeg webp'
        max_filesize: '10 MB'
        max_resolution: '2000x2000'
        min_resolution: '400x300'
        alt_field: true
        alt_field_required: true
        title_field: true
        
    # 服务地区 (多值)
    field_designer_locations:
      type: string
      label: 服务地区
      required: false
      cardinality: -1  # 多值
      settings:
        max_length: 100
        
    # 设计师评分
    field_designer_rating:
      type: decimal
      label: 设计师评分
      required: false
      settings:
        min: 0
        max: 5
        precision: 3
        scale: 1
        
    # 合作品牌 (关联到品牌内容类型)
    field_designer_brands:
      type: entity_reference
      label: 合作品牌
      required: false
      cardinality: -1  # 多值
      settings:
        target_type: node
        target_bundles:
          brand: brand
          
    # 代表作品 (关联到产品内容类型)
    field_designer_products:
      type: entity_reference
      label: 代表作品
      required: false
      cardinality: -1  # 多值
      settings:
        target_type: node
        target_bundles:
          product: product

# 4. V2.0显示配置 (智能优化)
display_settings:
  # 默认显示模式
  default:
    content:
      field_designer_avatar:
        type: image
        weight: 0
        settings:
          image_style: medium
      field_designer_bio:
        type: text_default
        weight: 1
      field_designer_specialties:
        type: entity_reference_label
        weight: 2
      field_designer_styles:
        type: entity_reference_label
        weight: 3
      field_designer_experience:
        type: number_integer
        weight: 4
        settings:
          suffix_display: true
      field_designer_contact:
        type: string
        weight: 5
      field_designer_website:
        type: link
        weight: 6
      field_designer_portfolio:
        type: image
        weight: 7
        settings:
          image_style: large
      field_designer_locations:
        type: string
        weight: 8
      field_designer_rating:
        type: number_decimal
        weight: 9
      field_designer_brands:
        type: entity_reference_label
        weight: 10
      field_designer_products:
        type: entity_reference_label
        weight: 11
        
  # 卡片显示模式
  teaser:
    content:
      field_designer_avatar:
        type: image
        weight: 0
        settings:
          image_style: thumbnail
      field_designer_specialties:
        type: entity_reference_label
        weight: 1
      field_designer_experience:
        type: number_integer
        weight: 2
        settings:
          suffix_display: true
      field_designer_rating:
        type: number_decimal
        weight: 3

# 5. V2.0表单显示配置 (批量优化)
form_display:
  content:
    title:
      type: string_textfield
      weight: 0
    field_designer_avatar:
      type: image_image
      weight: 1
    field_designer_bio:
      type: text_textarea_with_summary
      weight: 2
    field_designer_specialties:
      type: entity_reference_autocomplete_tags
      weight: 3
    field_designer_styles:
      type: entity_reference_autocomplete_tags
      weight: 4
    field_designer_experience:
      type: number
      weight: 5
    field_designer_contact:
      type: string_textfield
      weight: 6
    field_designer_website:
      type: link_default
      weight: 7
    field_designer_portfolio:
      type: image_image
      weight: 8
    field_designer_locations:
      type: string_textfield
      weight: 9
    field_designer_rating:
      type: number
      weight: 10
    field_designer_brands:
      type: entity_reference_autocomplete
      weight: 11
    field_designer_products:
      type: entity_reference_autocomplete
      weight: 12

# 6. V2.0权限配置
permissions:
  - create designer content
  - edit own designer content
  - edit any designer content
  - delete own designer content
  - delete any designer content
  - view published designer content
  - view unpublished designer content

# 7. V2.0关联关系配置
relationships:
  designer_to_brand:
    type: "many_to_many"
    description: "设计师可以与多个品牌合作"
  designer_to_product:
    type: "many_to_many"
    description: "设计师可以有多个代表作品"
  brand_to_designer:
    type: "many_to_many"
    description: "品牌可以有多个合作设计师"
  product_to_designer:
    type: "many_to_many"
    description: "产品可以有多个设计师"

# 8. V2.0性能优化配置
performance_optimization:
  field_caching: true
  entity_caching: true
  query_optimization: true
  image_optimization: true
  lazy_loading: true

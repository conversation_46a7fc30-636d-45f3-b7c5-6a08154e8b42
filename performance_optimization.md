# 性能优化建议

## 1. Drupal 性能优化

### 缓存配置
```php
// settings.php 缓存配置
$settings['cache']['bins']['render'] = 'cache.backend.redis';
$settings['cache']['bins']['data'] = 'cache.backend.redis';
$settings['cache']['bins']['discovery'] = 'cache.backend.redis';

// 启用页面缓存
$config['system.performance']['cache']['page']['max_age'] = 3600;

// 启用动态页面缓存
$config['system.performance']['cache']['page']['use_internal'] = TRUE;

// CSS/JS 聚合
$config['system.performance']['css']['preprocess'] = TRUE;
$config['system.performance']['js']['preprocess'] = TRUE;
```

### 数据库优化
```sql
-- 为常用查询添加索引
CREATE INDEX idx_node_type_status ON node_field_data (type, status);
CREATE INDEX idx_node_created ON node_field_data (created);
CREATE INDEX idx_taxonomy_term_name ON taxonomy_term_field_data (name);

-- 品牌相关索引
CREATE INDEX idx_brand_category ON node__field_brand_category (field_brand_category_target_id);
CREATE INDEX idx_brand_rating ON node__field_brand_rating (field_brand_rating_value);

-- 产品相关索引
CREATE INDEX idx_product_category ON node__field_product_category (field_product_category_target_id);
CREATE INDEX idx_product_brand ON node__field_product_brand (field_product_brand_target_id);
CREATE INDEX idx_product_price ON node__field_product_price (field_product_price_value);
```

### 图片优化
```yaml
图片样式配置:
  thumbnail:
    width: 150
    height: 150
    scale_and_crop: true
    quality: 85
    
  medium:
    width: 400
    height: 300
    scale_and_crop: true
    quality: 85
    
  large:
    width: 800
    height: 600
    scale_and_crop: true
    quality: 80
    
  hero:
    width: 1200
    height: 600
    scale_and_crop: true
    quality: 80

WebP支持:
  - 启用 WebP 图片格式
  - 配置自动转换
  - 设置回退机制
```

## 2. 前端性能优化

### Tailwind CSS 优化
```javascript
// tailwind.config.js 生产环境优化
module.exports = {
  content: [
    './templates/**/*.html.twig',
    './src/**/*.{js,jsx,ts,tsx}',
    './js/**/*.js',
  ],
  theme: {
    // 主题配置
  },
  plugins: [
    // 插件配置
  ],
  // 生产环境优化
  purge: {
    enabled: process.env.NODE_ENV === 'production',
    content: [
      './templates/**/*.html.twig',
      './src/**/*.{js,jsx,ts,tsx}',
      './js/**/*.js',
    ],
    options: {
      safelist: [
        // 保留的类名
        'favorited',
        'active',
        'show',
        'hide'
      ]
    }
  }
}
```

### JavaScript 优化
```javascript
// 懒加载图片
function initLazyLoading() {
  const images = document.querySelectorAll('img[data-src]');
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach(img => imageObserver.observe(img));
}

// 防抖搜索
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

const searchInput = document.getElementById('search-input');
const debouncedSearch = debounce(performSearch, 300);
searchInput.addEventListener('input', debouncedSearch);

// 虚拟滚动 (大列表优化)
class VirtualScroll {
  constructor(container, items, itemHeight) {
    this.container = container;
    this.items = items;
    this.itemHeight = itemHeight;
    this.visibleItems = Math.ceil(container.clientHeight / itemHeight) + 2;
    this.init();
  }

  init() {
    this.container.style.height = this.items.length * this.itemHeight + 'px';
    this.container.addEventListener('scroll', this.onScroll.bind(this));
    this.render();
  }

  onScroll() {
    this.render();
  }

  render() {
    const scrollTop = this.container.scrollTop;
    const startIndex = Math.floor(scrollTop / this.itemHeight);
    const endIndex = Math.min(startIndex + this.visibleItems, this.items.length);

    // 清空容器
    this.container.innerHTML = '';

    // 渲染可见项目
    for (let i = startIndex; i < endIndex; i++) {
      const item = this.createItem(this.items[i], i);
      this.container.appendChild(item);
    }
  }

  createItem(data, index) {
    const item = document.createElement('div');
    item.style.position = 'absolute';
    item.style.top = index * this.itemHeight + 'px';
    item.style.height = this.itemHeight + 'px';
    item.innerHTML = this.renderItemContent(data);
    return item;
  }
}
```

## 3. 服务器优化

### Apache/Nginx 配置
```nginx
# Nginx 配置示例
server {
    listen 80;
    server_name example.com;
    root /var/www/drupal;
    index index.php;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # PHP 处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # PHP 缓存头
        fastcgi_cache_valid 200 1h;
        fastcgi_cache_key "$scheme$request_method$host$request_uri";
    }

    # Drupal 特定配置
    location / {
        try_files $uri /index.php?$query_string;
    }

    location @rewrite {
        rewrite ^/(.*)$ /index.php?q=$1;
    }
}
```

### Redis 缓存配置
```php
// settings.php Redis 配置
$settings['redis.connection']['interface'] = 'PhpRedis';
$settings['redis.connection']['host'] = '127.0.0.1';
$settings['redis.connection']['port'] = 6379;
$settings['redis.connection']['base'] = 0;

$settings['cache']['default'] = 'cache.backend.redis';
$settings['cache']['bins']['bootstrap'] = 'cache.backend.chainedfast';
$settings['cache']['bins']['discovery'] = 'cache.backend.chainedfast';
$settings['cache']['bins']['config'] = 'cache.backend.chainedfast';

// 会话存储
$settings['session_redis'] = [
  'host' => '127.0.0.1',
  'port' => 6379,
  'database' => 1,
];
```

## 4. 监控和分析

### 性能监控工具
```javascript
// 页面加载性能监控
function trackPagePerformance() {
  window.addEventListener('load', () => {
    const perfData = performance.getEntriesByType('navigation')[0];
    
    // 发送性能数据到分析服务
    fetch('/api/analytics/performance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        page: window.location.pathname,
        loadTime: perfData.loadEventEnd - perfData.loadEventStart,
        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
        firstPaint: performance.getEntriesByType('paint')[0]?.startTime,
        firstContentfulPaint: performance.getEntriesByType('paint')[1]?.startTime,
        timestamp: Date.now()
      })
    });
  });
}

// 用户行为追踪
function trackUserInteractions() {
  // 点击追踪
  document.addEventListener('click', (e) => {
    if (e.target.matches('[data-track]')) {
      const action = e.target.dataset.track;
      const category = e.target.dataset.category || 'interaction';
      
      // 发送追踪数据
      gtag('event', action, {
        event_category: category,
        event_label: e.target.textContent || e.target.alt
      });
    }
  });

  // 滚动深度追踪
  let maxScroll = 0;
  window.addEventListener('scroll', () => {
    const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
    if (scrollPercent > maxScroll) {
      maxScroll = scrollPercent;
      if (maxScroll % 25 === 0) { // 每25%发送一次
        gtag('event', 'scroll_depth', {
          event_category: 'engagement',
          value: maxScroll
        });
      }
    }
  });
}

// 初始化监控
trackPagePerformance();
trackUserInteractions();
```

### Core Web Vitals 优化
```javascript
// 监控 Core Web Vitals
import {getCLS, getFID, getFCP, getLCP, getTTFB} from 'web-vitals';

function sendToAnalytics(metric) {
  gtag('event', metric.name, {
    event_category: 'Web Vitals',
    value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
    event_label: metric.id,
    non_interaction: true,
  });
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

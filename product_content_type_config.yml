# 产品内容类型配置文件
# Product Content Type Configuration

# 1. 产品分类词汇表配置
product_categories:
  vid: product_categories
  name: 产品分类
  description: 用于分类不同类型的家居产品
  terms:
    - 沙发座椅
    - 桌子茶几
    - 床具寝具
    - 储物收纳
    - 灯具照明
    - 装饰摆件
    - 厨房用具
    - 卫浴用品
    - 地毯窗帘
    - 智能家电

# 2. 产品标签词汇表配置
product_tags:
  vid: product_tags
  name: 产品标签
  description: 用于标记产品特性和风格
  terms:
    - 现代简约
    - 北欧风格
    - 中式古典
    - 欧式奢华
    - 工业风格
    - 田园风格
    - 环保材质
    - 智能科技
    - 多功能
    - 小户型适用

# 3. 产品内容类型字段配置
product_content_type:
  type: product
  name: 产品
  description: 家居产品信息管理
  
  fields:
    # 产品名称 (必需字段，已存在)
    title:
      type: string
      label: 产品名称
      required: true
      
    # 产品图片 (多值)
    field_product_images:
      type: image
      label: 产品图片
      required: true
      cardinality: -1  # 无限制
      settings:
        file_extensions: 'png gif jpg jpeg webp'
        max_filesize: '5 MB'
        max_resolution: '2000x2000'
        min_resolution: '300x300'
        alt_field: true
        alt_field_required: true
        title_field: true
        
    # 产品描述
    field_product_description:
      type: text_long
      label: 产品描述
      required: true
      settings:
        display_summary: true
        
    # 产品分类
    field_product_category:
      type: entity_reference
      label: 产品分类
      required: true
      settings:
        target_type: taxonomy_term
        target_bundles:
          product_categories: product_categories
          
    # 所属品牌
    field_product_brand:
      type: entity_reference
      label: 所属品牌
      required: true
      settings:
        target_type: node
        target_bundles:
          brand: brand
        
    # 产品价格
    field_product_price:
      type: decimal
      label: 产品价格
      required: true
      settings:
        min: 0
        precision: 10
        scale: 2
        
    # 产品规格参数
    field_product_specifications:
      type: text_long
      label: 产品规格
      required: false
      settings:
        display_summary: false
        
    # 产品标签
    field_product_tags:
      type: entity_reference
      label: 产品标签
      required: false
      cardinality: -1  # 多值
      settings:
        target_type: taxonomy_term
        target_bundles:
          product_tags: product_tags
          
    # 产品库存
    field_product_stock:
      type: integer
      label: 库存数量
      required: false
      settings:
        min: 0
        
    # 产品状态
    field_product_status:
      type: list_string
      label: 产品状态
      required: true
      settings:
        allowed_values:
          available: 有货
          out_of_stock: 缺货
          discontinued: 停产
          pre_order: 预订

# 4. 显示配置
display_settings:
  # 默认显示模式
  default:
    content:
      field_product_images:
        type: image
        weight: 0
        settings:
          image_style: large
      field_product_description:
        type: text_default
        weight: 1
      field_product_brand:
        type: entity_reference_label
        weight: 2
      field_product_category:
        type: entity_reference_label
        weight: 3
      field_product_price:
        type: number_decimal
        weight: 4
        settings:
          prefix_suffix: true
          thousand_separator: ','
          decimal_separator: '.'
      field_product_specifications:
        type: text_default
        weight: 5
      field_product_tags:
        type: entity_reference_label
        weight: 6
      field_product_stock:
        type: number_integer
        weight: 7
      field_product_status:
        type: list_default
        weight: 8
        
  # 预览显示模式
  teaser:
    content:
      field_product_images:
        type: image
        weight: 0
        settings:
          image_style: medium
      field_product_brand:
        type: entity_reference_label
        weight: 1
      field_product_category:
        type: entity_reference_label
        weight: 2
      field_product_price:
        type: number_decimal
        weight: 3
        settings:
          prefix_suffix: true
      field_product_status:
        type: list_default
        weight: 4

# 5. 表单显示配置
form_display:
  content:
    title:
      type: string_textfield
      weight: 0
    field_product_images:
      type: image_image
      weight: 1
    field_product_description:
      type: text_textarea_with_summary
      weight: 2
    field_product_brand:
      type: entity_reference_autocomplete
      weight: 3
    field_product_category:
      type: options_select
      weight: 4
    field_product_price:
      type: number
      weight: 5
    field_product_specifications:
      type: text_textarea
      weight: 6
    field_product_tags:
      type: entity_reference_autocomplete_tags
      weight: 7
    field_product_stock:
      type: number
      weight: 8
    field_product_status:
      type: options_select
      weight: 9

# 6. 权限配置
permissions:
  - create product content
  - edit own product content
  - edit any product content
  - delete own product content
  - delete any product content
  - view published product content
  - view unpublished product content

# 7. 视图配置建议
views_suggestions:
  product_list:
    - 按分类筛选
    - 按品牌筛选
    - 按价格范围筛选
    - 按标签筛选
    - 按库存状态筛选
  product_search:
    - 全文搜索
    - 高级筛选
    - 排序选项
  brand_products:
    - 显示特定品牌的所有产品
    - 关联品牌信息

<?php return array(
    'root' => array(
        'name' => 'drupal/legacy-project',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'asm89/stack-cors' => array(
            'pretty_version' => 'v2.2.0',
            'version' => '2.2.0.0',
            'reference' => '50f57105bad3d97a43ec4a485eb57daf347eafea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../asm89/stack-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/installers' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '2.3.0.0',
            'reference' => '12fb2dfe5e16183de69e784a7b84046c43d97e8e',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./installers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/annotations' => array(
            'pretty_version' => '1.14.4',
            'version' => '1.14.4.0',
            'reference' => '253dca476f70808a5aeed3a47cc2cc88c5cab915',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/annotations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core' => array(
            'pretty_version' => '10.4.7',
            'version' => '10.4.7.0',
            'reference' => '547fa74348dda2ecb4a3e752f88a5c40be675d64',
            'type' => 'drupal-core',
            'install_path' => __DIR__ . '/../../core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-annotation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-assertion' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-class-finder' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-composer-scaffold' => array(
            'pretty_version' => '10.4.7',
            'version' => '10.4.7.0',
            'reference' => 'db17b59620ce1c142a34dc017d9e696ce4771e55',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../drupal/core-composer-scaffold',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-datetime' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-dependency-injection' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-diff' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-discovery' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-event-dispatcher' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-file-cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-file-security' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-front-matter' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-gettext' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-graph' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-http-foundation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-php-storage' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-plugin' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-project-message' => array(
            'pretty_version' => '10.4.7',
            'version' => '10.4.7.0',
            'reference' => 'd1da83722735cb0f7ccabf9fef7b5607b442c3a8',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../drupal/core-project-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-proxy-builder' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-recommended' => array(
            'pretty_version' => '10.4.7',
            'version' => '10.4.7.0',
            'reference' => '308b63fa05111c15f4a36919718b7c2d016af892',
            'type' => 'metapackage',
            'install_path' => null,
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-render' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-serialization' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-transliteration' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-utility' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-vendor-hardening' => array(
            'pretty_version' => '10.4.7',
            'version' => '10.4.7.0',
            'reference' => '82bb33f8da23130afa412e239c72ce70a8dba690',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../drupal/core-vendor-hardening',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-version' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/ctools' => array(
            'pretty_version' => '4.1.0',
            'version' => '4.1.0.0',
            'reference' => '4.1.0',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../modules/contrib/ctools',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/entity_reference_revisions' => array(
            'pretty_version' => '1.12.0',
            'version' => '1.12.0.0',
            'reference' => '8.x-1.12',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../modules/contrib/entity_reference_revisions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/field_group' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => '4.0.0',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../modules/contrib/field_group',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/legacy-project' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/metatag' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '2.1.1',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../modules/contrib/metatag',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/paragraphs' => array(
            'pretty_version' => '1.19.0',
            'version' => '1.19.0.0',
            'reference' => '8.x-1.19',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../modules/contrib/paragraphs',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/pathauto' => array(
            'pretty_version' => '1.13.0',
            'version' => '1.13.0.0',
            'reference' => '8.x-1.13',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../modules/contrib/pathauto',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/search_api' => array(
            'pretty_version' => '1.38.0',
            'version' => '1.38.0.0',
            'reference' => '8.x-1.38',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../modules/contrib/search_api',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/token' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'reference' => '8.x-1.15',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../modules/contrib/token',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/webform' => array(
            'pretty_version' => '6.2.9',
            'version' => '6.2.9.0',
            'reference' => '6.2.9',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../modules/contrib/webform',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd42c8731f0624ad6bdc8d3e5e9a4524f68801cfa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'f9c436286ab2892c7db7be8c8da4ef61ccf7b455',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mck89/peast' => array(
            'pretty_version' => 'v1.16.3',
            'version' => '1.16.3.0',
            'reference' => '645ec21b650bc2aced18285c85f220d22afc1430',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mck89/peast',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/archive_tar' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'reference' => 'b439c859564f5cbb0f64ad6002d0afe84a889602',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/archive_tar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/console_getopt' => array(
            'pretty_version' => 'v1.4.3',
            'version' => '1.4.3.0',
            'reference' => 'a41f8d3e668987609178c7c4a9fe48fecac53fa0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/console_getopt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/pear-core-minimal' => array(
            'pretty_version' => 'v1.10.16',
            'version' => '1.10.16.0',
            'reference' => 'c0f51b45f50683bf5bbf558036854ebc9b54d033',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/pear-core-minimal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/pear_exception' => array(
            'pretty_version' => 'v1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'b14fbe2ddb0b9f94f5b24cf08783d599f776fff0',
            'type' => 'class',
            'install_path' => __DIR__ . '/../pear/pear_exception',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rsky/pear-core-min' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v1.10.16',
            ),
        ),
        'sebastian/diff' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => 'ba01945089c3a293b01ba9badc29ad55b106b0bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => '7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dependency-injection' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => '8cb11f833d1f5bfbb2df97dfc23c92b4d42c18d9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dependency-injection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => 'ce765a2d28b3cce61de1fb916e207767a73171d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '7642f5e970b672283b7823222ae8ef8bbc160b9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '4856c9cf585d5a0313d8d35afd681a526f038dd3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.4.17',
            'version' => '6.4.17.0',
            'reference' => '1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => '6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => '15c105b839a7cfa1bc0989c091bfb6477f23b673',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'ada2809ccd4ec27aba9fc344e3efdaec624c6438',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'fec8aa5231f3904754955fad33c2db50594d22d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '48becf00c920479ca2e910c22a5a39e5d47ca956',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'c36586dcf89a12315939e00ec9b4474adcb1d773',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v6.4.20',
            'version' => '6.4.20.0',
            'reference' => 'e2a61c16af36c9a07e5c9906498b73e091949a20',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/psr-http-message-bridge' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => 'c9cf83326a1074f83a738fc5320945abf7fb7fec',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/psr-http-message-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => '1f5234e8457164a3a0038a4c0a4ba27876a9c670',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/serializer' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => 'b836df93e9ea07d1d3ada58a679ef205d54b64d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/serializer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => 'e53260aabf78fb3d63f8d79d69ece59f80d5eda0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => '73e2c6966a5aef1d4892873ed5322245295370c6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '4667ff3bd513750603a09c8dedbea942487fb07c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/validator' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => '4c5fbccb2d8f64017c8dada6473701a5c8539716',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => '22560f80c0c5cd58cc0bcaf73455ffd81eb380d5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => 'f28cf841f5654955c9f88ceaf4b9dc29571988a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'f01987f45676778b474468aa266fe2eda1f2bc7e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.19.0',
            'version' => '3.19.0.0',
            'reference' => 'd4f8c2b86374f08efc859323dbcd95c590f7124e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);

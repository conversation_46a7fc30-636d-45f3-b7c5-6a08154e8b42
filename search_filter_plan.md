# 搜索和筛选功能规划

## 1. 搜索功能实现

### 全站搜索
```yaml
搜索范围:
  - 品牌名称和描述
  - 产品名称和描述
  - 设计师姓名和简介
  - 资讯文章标题和内容

搜索配置:
  - 使用 Drupal Search API 模块
  - 配置 Solr 或 Database 后端
  - 设置中文分词支持
  - 配置搜索权重和相关性
```

### 搜索建议和自动完成
```javascript
// 搜索自动完成功能
function initSearchAutocomplete() {
  $('#search-input').autocomplete({
    source: function(request, response) {
      $.ajax({
        url: '/api/search/autocomplete',
        data: { term: request.term },
        success: function(data) {
          response(data);
        }
      });
    },
    minLength: 2,
    select: function(event, ui) {
      window.location.href = ui.item.url;
    }
  });
}
```

## 2. 高级筛选功能

### 品牌筛选器
```yaml
筛选条件:
  - 品牌分类 (多选)
  - 评分范围 (滑块)
  - 字母排序 (A-Z)
  - 产品数量范围

排序选项:
  - 评分高低
  - 产品数量
  - 创建时间
  - 字母顺序
```

### 产品筛选器
```yaml
筛选条件:
  - 产品分类 (多选)
  - 品牌 (多选)
  - 价格范围 (滑块)
  - 评分范围 (滑块)
  - 颜色 (色块选择)
  - 材质 (多选)
  - 尺寸规格

排序选项:
  - 价格高低
  - 评分高低
  - 最新上架
  - 销量排序
  - 人气排序
```

### 设计师筛选器
```yaml
筛选条件:
  - 专业领域 (多选)
  - 服务地区 (多选)
  - 从业年限 (范围)
  - 评分范围 (滑块)
  - 设计风格 (多选)

排序选项:
  - 评分高低
  - 从业年限
  - 作品数量
  - 最近活跃
```

## 3. 筛选器 UI 组件

### 价格范围滑块
```html
<div class="price-filter">
  <label class="block text-sm font-medium text-gray-700 mb-2">价格范围</label>
  <div class="px-3">
    <div id="price-slider" class="mb-4"></div>
    <div class="flex justify-between text-sm text-gray-600">
      <span>¥<span id="min-price">0</span></span>
      <span>¥<span id="max-price">10000</span></span>
    </div>
  </div>
</div>
```

### 多选分类筛选
```html
<div class="category-filter">
  <label class="block text-sm font-medium text-gray-700 mb-2">产品分类</label>
  <div class="space-y-2 max-h-48 overflow-y-auto">
    <label class="flex items-center">
      <input type="checkbox" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" value="sofa">
      <span class="ml-2 text-sm text-gray-700">沙发</span>
      <span class="ml-auto text-xs text-gray-500">(123)</span>
    </label>
    <!-- 更多分类选项 -->
  </div>
</div>
```

### 评分筛选
```html
<div class="rating-filter">
  <label class="block text-sm font-medium text-gray-700 mb-2">评分</label>
  <div class="space-y-1">
    <label class="flex items-center">
      <input type="radio" name="rating" class="text-primary-600 focus:ring-primary-500" value="5">
      <div class="ml-2 flex items-center">
        <div class="flex text-yellow-400">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
          </svg>
          <!-- 重复4次 -->
        </div>
        <span class="ml-1 text-sm text-gray-600">及以上</span>
      </div>
    </label>
    <!-- 其他评分选项 -->
  </div>
</div>
```

## 4. 筛选结果展示

### 已选筛选条件显示
```html
<div class="selected-filters mb-4">
  <div class="flex flex-wrap gap-2">
    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800">
      沙发
      <button class="ml-1 text-primary-600 hover:text-primary-800">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    </span>
    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800">
      ¥1000-5000
      <button class="ml-1 text-primary-600 hover:text-primary-800">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    </span>
    <button class="text-sm text-gray-500 hover:text-gray-700">清除所有</button>
  </div>
</div>
```

### 结果统计和排序
```html
<div class="results-header flex justify-between items-center mb-6">
  <div class="results-count">
    <span class="text-gray-600">找到 <strong class="text-gray-900">1,234</strong> 个结果</span>
  </div>
  <div class="sort-options">
    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-primary-500 focus:border-primary-500">
      <option value="relevance">相关性</option>
      <option value="price_asc">价格从低到高</option>
      <option value="price_desc">价格从高到低</option>
      <option value="rating_desc">评分从高到低</option>
      <option value="newest">最新上架</option>
    </select>
  </div>
</div>
```

## 5. 移动端筛选优化

### 筛选抽屉
```html
<div class="mobile-filter-drawer fixed inset-0 z-50 hidden" id="filter-drawer">
  <div class="absolute inset-0 bg-black bg-opacity-50" onclick="closeFilterDrawer()"></div>
  <div class="absolute right-0 top-0 h-full w-80 bg-white shadow-xl transform translate-x-full transition-transform duration-300">
    <div class="flex items-center justify-between p-4 border-b">
      <h3 class="text-lg font-semibold">筛选条件</h3>
      <button onclick="closeFilterDrawer()" class="text-gray-400 hover:text-gray-600">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    </div>
    <div class="p-4 overflow-y-auto h-full pb-20">
      <!-- 筛选器内容 -->
    </div>
    <div class="absolute bottom-0 left-0 right-0 p-4 bg-white border-t">
      <div class="flex space-x-3">
        <button class="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-md">重置</button>
        <button class="flex-1 bg-primary-600 text-white py-2 px-4 rounded-md">应用筛选</button>
      </div>
    </div>
  </div>
</div>
```

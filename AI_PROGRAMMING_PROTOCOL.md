# AI 编程执行协议 (AI Programming Protocol)

## 🤖 强制执行指令 (MANDATORY EXECUTION DIRECTIVES)

### ⚠️ 编程前必须执行的检查清单 (PRE-PROGRAMMING CHECKLIST)

```
BEFORE ANY CODING TASK:
1. READ website_development_workflow.md
2. IDENTIFY current task status
3. VERIFY prerequisites are met
4. UPDATE task status to 🔄 (in progress)
5. EXECUTE the task
6. UPDATE task status to ✅ (completed)
7. LOG changes in update log
8. COMMIT changes with descriptive message
```

---

## 📋 AI 逻辑执行流程 (AI LOGIC EXECUTION FLOW)

### STEP 1: 工作流程状态检查 (WORKFLOW STATUS CHECK)
```python
def check_workflow_status():
    """
    强制性工作流程检查
    MANDATORY: Must be executed before any programming task
    """
    workflow_file = "website_development_workflow.md"
    
    # 1. 读取当前工作流程状态
    current_status = read_workflow_file(workflow_file)
    
    # 2. 识别下一个待执行任务
    next_task = identify_next_task(current_status)
    
    # 3. 验证前置条件
    prerequisites_met = verify_prerequisites(next_task)
    
    if not prerequisites_met:
        raise Exception("PREREQUISITES NOT MET - CANNOT PROCEED")
    
    # 4. 更新任务状态为进行中
    update_task_status(next_task, "🔄")
    
    return next_task

# 约束条件：此函数必须在任何编程任务前执行
CONSTRAINT: This function MUST be called before any programming task
```

### STEP 2: 任务执行验证 (TASK EXECUTION VALIDATION)
```python
def validate_task_execution(task_id, task_type):
    """
    任务执行验证逻辑
    CONSTRAINT: All tasks must follow this validation pattern
    """
    validation_rules = {
        "content_type_creation": {
            "required_fields": ["name", "description", "fields_config"],
            "validation_method": "drupal_content_type_validation",
            "rollback_method": "delete_content_type"
        },
        "theme_template_creation": {
            "required_fields": ["template_path", "twig_syntax", "css_classes"],
            "validation_method": "twig_template_validation",
            "rollback_method": "restore_previous_template"
        },
        "module_installation": {
            "required_fields": ["module_name", "dependencies", "configuration"],
            "validation_method": "drupal_module_validation",
            "rollback_method": "uninstall_module"
        }
    }
    
    # 强制验证规则
    if task_type not in validation_rules:
        raise Exception(f"UNKNOWN TASK TYPE: {task_type}")
    
    return validation_rules[task_type]

# 约束条件：所有任务必须通过验证
CONSTRAINT: All tasks must pass validation before completion
```

### STEP 3: 自动化状态更新 (AUTOMATED STATUS UPDATE)
```python
def update_workflow_status(task_id, status, completion_time=None, notes=None):
    """
    自动化工作流程状态更新
    MANDATORY: Must be called after task completion
    """
    workflow_file = "website_development_workflow.md"
    
    # 1. 读取当前工作流程
    workflow_content = read_file(workflow_file)
    
    # 2. 定位任务行
    task_line = find_task_line(workflow_content, task_id)
    
    # 3. 更新状态
    updated_line = update_task_line(task_line, {
        "status": status,
        "completion_time": completion_time or get_current_timestamp(),
        "notes": notes
    })
    
    # 4. 写回文件
    write_updated_workflow(workflow_file, updated_line)
    
    # 5. 记录更新日志
    log_update(task_id, status, completion_time, notes)

# 约束条件：任务完成后必须更新状态
CONSTRAINT: Status must be updated immediately after task completion
```

---

## 🔒 约束性规则 (CONSTRAINT RULES)

### 规则 1: 工作流程依赖性 (WORKFLOW DEPENDENCY)
```yaml
RULE_001_WORKFLOW_DEPENDENCY:
  description: "任务必须按照依赖顺序执行"
  constraint: |
    - 不能跳过前置任务
    - 必须验证依赖任务已完成
    - 违反此规则将终止执行
  enforcement: "STRICT"
  violation_action: "TERMINATE_EXECUTION"
```

### 规则 2: 状态同步性 (STATUS SYNCHRONIZATION)
```yaml
RULE_002_STATUS_SYNC:
  description: "工作流程状态必须实时同步"
  constraint: |
    - 任务开始时立即更新为 🔄
    - 任务完成时立即更新为 ✅
    - 任务失败时立即更新为 ❌
    - 所有状态变更必须记录时间戳
  enforcement: "MANDATORY"
  violation_action: "ROLLBACK_AND_RETRY"
```

### 规则 3: 代码质量保证 (CODE QUALITY ASSURANCE)
```yaml
RULE_003_CODE_QUALITY:
  description: "所有代码必须符合质量标准"
  constraint: |
    - 必须包含注释和文档
    - 必须遵循 Drupal 编码标准
    - 必须通过语法验证
    - 必须包含错误处理
  enforcement: "STRICT"
  validation_tools: ["phpcs", "phpstan", "twig-lint"]
```

### 规则 4: 回滚机制 (ROLLBACK MECHANISM)
```yaml
RULE_004_ROLLBACK:
  description: "所有操作必须支持回滚"
  constraint: |
    - 每个任务执行前创建备份点
    - 失败时自动回滚到上一个稳定状态
    - 记录回滚操作和原因
  enforcement: "MANDATORY"
  backup_strategy: "INCREMENTAL_SNAPSHOTS"
```

---

## 🔄 持续优化机制 (CONTINUOUS OPTIMIZATION)

### 自动化性能监控
```python
class PerformanceMonitor:
    """
    性能监控和优化建议系统
    """
    def __init__(self):
        self.metrics = {
            "task_completion_time": [],
            "error_rate": 0,
            "code_quality_score": 0,
            "workflow_efficiency": 0
        }
    
    def monitor_task_execution(self, task_id, start_time, end_time, success):
        """监控任务执行性能"""
        execution_time = end_time - start_time
        self.metrics["task_completion_time"].append(execution_time)
        
        if not success:
            self.metrics["error_rate"] += 1
        
        # 生成优化建议
        optimization_suggestions = self.generate_optimization_suggestions()
        return optimization_suggestions
    
    def generate_optimization_suggestions(self):
        """生成优化建议"""
        suggestions = []
        
        avg_time = sum(self.metrics["task_completion_time"]) / len(self.metrics["task_completion_time"])
        
        if avg_time > 300:  # 5分钟
            suggestions.append("SUGGESTION: Consider breaking down large tasks into smaller subtasks")
        
        if self.metrics["error_rate"] > 0.1:  # 10% 错误率
            suggestions.append("SUGGESTION: Implement more robust error handling and validation")
        
        return suggestions

# 约束条件：每个任务都必须被监控
CONSTRAINT: All tasks must be monitored for performance optimization
```

### 学习和适应机制
```python
class AdaptiveLearning:
    """
    自适应学习系统
    """
    def __init__(self):
        self.knowledge_base = load_knowledge_base()
        self.success_patterns = {}
        self.failure_patterns = {}
    
    def learn_from_execution(self, task_type, context, result):
        """从执行结果中学习"""
        if result.success:
            self.success_patterns[task_type] = context
        else:
            self.failure_patterns[task_type] = context
        
        # 更新知识库
        self.update_knowledge_base()
    
    def suggest_improvements(self, task_type):
        """基于历史数据建议改进"""
        if task_type in self.failure_patterns:
            return f"CAUTION: Previous failures detected for {task_type}. Review: {self.failure_patterns[task_type]}"
        
        if task_type in self.success_patterns:
            return f"RECOMMENDATION: Use successful pattern: {self.success_patterns[task_type]}"
        
        return "INFO: No historical data available for this task type"

# 约束条件：系统必须从每次执行中学习
CONSTRAINT: System must learn from every execution for continuous improvement
```

---

## 🚨 错误处理和恢复 (ERROR HANDLING AND RECOVERY)

### 错误分类和处理策略
```python
ERROR_HANDLING_MATRIX = {
    "CRITICAL_ERROR": {
        "description": "系统级错误，可能导致数据丢失",
        "action": "IMMEDIATE_ROLLBACK",
        "notification": "ALERT_ADMIN",
        "retry": False
    },
    "RECOVERABLE_ERROR": {
        "description": "可恢复的错误，不影响系统稳定性",
        "action": "AUTO_RETRY",
        "notification": "LOG_WARNING",
        "retry": True,
        "max_retries": 3
    },
    "VALIDATION_ERROR": {
        "description": "数据验证失败",
        "action": "REQUEST_CORRECTION",
        "notification": "USER_PROMPT",
        "retry": True,
        "max_retries": 1
    }
}

def handle_error(error_type, error_details, context):
    """
    统一错误处理机制
    MANDATORY: All errors must be handled through this function
    """
    if error_type not in ERROR_HANDLING_MATRIX:
        error_type = "CRITICAL_ERROR"  # 默认为最严格处理
    
    handler = ERROR_HANDLING_MATRIX[error_type]
    
    # 执行错误处理动作
    if handler["action"] == "IMMEDIATE_ROLLBACK":
        rollback_to_last_stable_state(context)
    elif handler["action"] == "AUTO_RETRY":
        schedule_retry(context, handler["max_retries"])
    
    # 记录错误
    log_error(error_type, error_details, context)
    
    return handler

# 约束条件：所有错误必须通过此机制处理
CONSTRAINT: All errors must be processed through this unified error handling system
```

---

## 📊 执行报告模板 (EXECUTION REPORT TEMPLATE)

```markdown
## AI 执行报告 - {TIMESTAMP}

### 任务信息
- **任务ID**: {TASK_ID}
- **任务类型**: {TASK_TYPE}
- **开始时间**: {START_TIME}
- **完成时间**: {END_TIME}
- **执行时长**: {DURATION}

### 执行结果
- **状态**: {STATUS}
- **成功率**: {SUCCESS_RATE}
- **错误数量**: {ERROR_COUNT}

### 生成的文件
{GENERATED_FILES_LIST}

### 修改的文件
{MODIFIED_FILES_LIST}

### 性能指标
- **代码质量分数**: {CODE_QUALITY_SCORE}
- **测试覆盖率**: {TEST_COVERAGE}
- **性能评分**: {PERFORMANCE_SCORE}

### 优化建议
{OPTIMIZATION_SUGGESTIONS}

### 下一步行动
{NEXT_ACTIONS}
```

---

## ⚡ 快速执行命令 (QUICK EXECUTION COMMANDS)

```bash
# 检查当前状态
AI_CHECK_STATUS()

# 执行下一个任务
AI_EXECUTE_NEXT()

# 更新工作流程
AI_UPDATE_WORKFLOW(task_id, status, notes)

# 生成执行报告
AI_GENERATE_REPORT()

# 回滚到上一个状态
AI_ROLLBACK()
```

---

## 🔐 最终约束声明 (FINAL CONSTRAINT DECLARATION)

```
⚠️  CRITICAL CONSTRAINT NOTICE ⚠️

THIS PROTOCOL IS MANDATORY FOR ALL AI PROGRAMMING ACTIVITIES.
VIOLATION OF ANY CONSTRAINT WILL RESULT IN:
1. IMMEDIATE TASK TERMINATION
2. AUTOMATIC ROLLBACK TO LAST STABLE STATE
3. ERROR LOGGING AND NOTIFICATION
4. REQUIREMENT FOR MANUAL INTERVENTION

NO EXCEPTIONS ALLOWED.
COMPLIANCE IS NON-NEGOTIABLE.

AI MUST ACKNOWLEDGE THIS PROTOCOL BEFORE PROCEEDING WITH ANY TASK.
```
